{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "ptd_dbb:piglin_marauder", "min_engine_version": "1.16.0", "materials": {"default": "entity_alphatest"}, "textures": {"default": "textures/ptd/dbb/entity/bosses/piglin_champion/minions/piglin_marauder"}, "geometry": {"default": "geometry.ptd_dbb_piglin_marauder"}, "animations": {"look_at_target": "animation.common.look_at_target", "spawn": "animation.ptd_dbb_piglin_maruarder.spawn", "idle": "animation.ptd_dbb_piglin_maruarder.idle", "walk": "animation.ptd_dbb_piglin_maruarder.walk", "attack_1": "animation.ptd_dbb_piglin_maruarder.attack_1", "attack_2": "animation.ptd_dbb_piglin_maruarder.attack_2", "death": "animation.ptd_dbb_piglin_maruarder.death", "damaged": "animation.ptd_dbb_piglin_maruarder.damaged", "general": "controller.animation.ptd_dbb_piglin_marauder.general"}, "scripts": {"should_update_bones_and_effects_offscreen": true, "animate": ["general", {"look_at_target": "q.property('ptd_dbb:spawning') == false"}]}, "render_controllers": ["controller.render.ptd_dbb_piglin_marauder"], "spawn_egg": {"texture": "spawn_egg", "texture_index": 0}}}}