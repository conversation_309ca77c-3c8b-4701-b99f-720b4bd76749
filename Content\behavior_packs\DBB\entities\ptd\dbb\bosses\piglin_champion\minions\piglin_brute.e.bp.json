{"format_version": "1.21.80", "minecraft:entity": {"description": {"identifier": "ptd_dbb:piglin_brute", "is_spawnable": true, "is_summonable": true, "properties": {"ptd_dbb:spawning": {"type": "bool", "client_sync": true, "default": true}, "ptd_dbb:dead": {"type": "bool", "client_sync": true, "default": false}, "ptd_dbb:death_timer": {"type": "int", "client_sync": true, "default": 0, "range": [0, 100]}}}, "component_groups": {"ptd_dbb:spawning": {"minecraft:physics": {}, "minecraft:is_collidable": {}, "minecraft:timer": {"time": 1.0, "looping": false, "time_down_event": {"event": "ptd_dbb:on_spawn", "target": "self"}}}, "ptd_dbb:default": {"minecraft:physics": {}, "minecraft:attack": {"damage": 6}, "minecraft:behavior.delayed_attack": {"priority": 1, "attack_once": false, "track_target": true, "require_complete_path": false, "random_stop_interval": 0, "reach_multiplier": 1.8, "speed_multiplier": 1.18, "attack_duration": 1.5, "hit_delay_pct": 0.5, "cooldown_time": 0}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:navigation.walk": {"can_pass_doors": true, "can_break_doors": true, "can_jump": true, "can_path_over_water": true, "avoid_damage_blocks": true, "avoid_water": false}, "minecraft:behavior.nearest_attackable_target": {"priority": 1, "must_see": false, "reselect_targets": true, "within_radius": 25.0, "must_see_forget_duration": 3.0, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "player"}, "max_dist": 35}]}, "minecraft:behavior.hurt_by_target": {"priority": 1}, "minecraft:behavior.random_look_around": {"priority": 9}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 6.0, "probability": 0.02}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 1.0}, "minecraft:damage_sensor": {"triggers": [{"on_damage": {"filters": {"test": "actor_health", "subject": "self", "operator": "<=", "value": 2}, "event": "ptd_dbb:dead", "target": "self"}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "bool_property", "domain": "ptd_dbb:dead", "subject": "self", "value": true}}, "deals_damage": "no"}]}}, "ptd_dbb:dead": {"minecraft:physics": {}, "minecraft:is_collidable": {}, "minecraft:timer": {"time": 2.0, "looping": false, "time_down_event": {"event": "ptd_dbb:despawn", "target": "self"}}, "minecraft:movement": {"max": 0}, "minecraft:behavior.random_look_around": {"priority": 999999}, "minecraft:behavior.random_stroll": {"priority": 999999}, "minecraft:body_rotation_blocked": {}, "minecraft:damage_sensor": {"triggers": [{"on_damage": {"filters": {"test": "actor_health", "subject": "self", "operator": "<=", "value": 2}, "event": "ptd_dbb:dead", "target": "self"}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "bool_property", "domain": "ptd_dbb:dead", "subject": "self", "value": true}}, "deals_damage": "no"}]}}, "ptd_dbb:despawn": {"minecraft:instant_despawn": {}}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["ptd_dbb:spawning"]}}, "ptd_dbb:on_spawn": {"sequence": [{"remove": {"component_groups": ["ptd_dbb:spawning"]}}, {"set_property": {"ptd_dbb:spawning": false}}, {"add": {"component_groups": ["ptd_dbb:default"]}}]}, "ptd_dbb:dead": {"sequence": [{"set_property": {"ptd_dbb:dead": true}}, {"remove": {"component_groups": ["ptd_dbb:default"]}}, {"add": {"component_groups": ["ptd_dbb:dead", "ptd_dbb:default"]}}]}, "ptd_dbb:despawn": {"sequence": [{"add": {"component_groups": ["ptd_dbb:despawn"]}}]}}, "components": {"minecraft:collision_box": {"width": 0.6, "height": 1.9}, "minecraft:type_family": {"family": ["piglin_brute", "monster", "hostile", "minion", "piglin_champion"]}, "minecraft:damage_sensor": {"triggers": [{"on_damage": {"filters": {"test": "actor_health", "subject": "self", "operator": "<=", "value": 2}, "event": "ptd_dbb:dead", "target": "self"}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "bool_property", "domain": "ptd_dbb:spawning", "subject": "self", "value": true}}, "deals_damage": "no"}, {"on_damage": {"filters": {"test": "bool_property", "domain": "ptd_dbb:dead", "subject": "self", "value": true}}, "deals_damage": "no"}]}, "minecraft:health": {"value": 30, "max": 30}, "minecraft:movement": {"max": 0.25}, "minecraft:underwater_movement": {"value": 0.02}, "minecraft:behavior.float": {"priority": 0}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {}}}}