{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ptd_dbb_piglin_marauder.general": {"states": {"default": {"animations": [{"spawn": "q.property('ptd_dbb:spawning') == true"}], "transitions": [{"idling": "q.property('ptd_dbb:spawning') == false"}], "blend_transition": 0.3}, "idling": {"animations": ["idle"], "transitions": [{"moving": "q.ground_speed > 0.3"}, {"attacking_slam": "q.property('ptd_dbb:attack') == 'slam'"}, {"attacking_sweep": "q.property('ptd_dbb:attack') == 'sweep'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "moving": {"animations": ["walk"], "transitions": [{"idling": "q.ground_speed < 0.3"}, {"attacking_slam": "q.property('ptd_dbb:attack') == 'slam'"}, {"attacking_sweep": "q.property('ptd_dbb:attack') == 'sweep'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "attacking_slam": {"animations": ["attack_1"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "attacking_sweep": {"animations": ["attack_2"], "transitions": [{"idling": "q.property('ptd_dbb:attack') == 'none'"}, {"dead": "q.property('ptd_dbb:dead') == true"}], "blend_transition": 0.3}, "dead": {"animations": ["death"]}}}}}