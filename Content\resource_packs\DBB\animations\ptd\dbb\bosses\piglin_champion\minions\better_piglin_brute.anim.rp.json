{"format_version": "1.8.0", "animations": {"animation.ptd_dbb_piglin_brute.idle": {"loop": true, "animation_length": 2, "bones": {"head": {"rotation": {"0.0": [2.5, 0, 0], "0.0833": [2.5, 0, 0], "0.1667": [2.5, 0, 0], "0.25": [2.5, 0, 0], "0.3333": [2.5, 0, 0], "0.4167": [2.5, 0, 0], "0.5": [2.5, 0, 0], "0.5833": [2.5, 0, 0], "0.6667": [2.5, 0, 0], "0.75": [2.5, 0, 0], "0.8333": [2.5, 0, 0], "0.9167": [2.5, 0, 0], "1.0": [2.5, 0, 0], "1.0833": [2.5, 0, 0], "1.1667": [2.5, 0, 0], "1.25": [2.5, 0, 0], "1.3333": [2.5, 0, 0], "1.4167": [2.5, 0, 0], "1.5": [2.5, 0, 0], "1.5833": [2.5, 0, 0], "1.6667": [2.5, 0, 0], "1.75": [2.5, 0, 0], "1.8333": [2.5, 0, 0], "1.9167": [2.5, 0, 0], "2.0": [2.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0], "1.0833": [0, 0, 0], "1.1667": [0, 0, 0], "1.25": [0, 0, 0], "1.3333": [0, 0, 0], "1.4167": [0, 0, 0], "1.5": [0, 0, 0], "1.5833": [0, 0, 0], "1.6667": [0, 0, 0], "1.75": [0, 0, 0], "1.8333": [0, 0, 0], "1.9167": [0, 0, 0], "2.0": [0, 0, 0]}}, "left_ear": {"rotation": {"0.0": [0, 0, -18.5], "0.0833": [0, 0, -18.64], "0.1667": [0, 0, -19.04], "0.25": [0, 0, -19.67], "0.3333": [0, 0, -20.5], "0.4167": [0, 0, -21.46], "0.5": [0, 0, -22.5], "0.5833": [0, 0, -23.54], "0.6667": [0, 0, -24.5], "0.75": [0, 0, -25.33], "0.8333": [0, 0, -25.96], "0.9167": [0, 0, -26.36], "1.0": [0, 0, -26.5], "1.0833": [0, 0, -26.36], "1.1667": [0, 0, -25.96], "1.25": [0, 0, -25.33], "1.3333": [0, 0, -24.5], "1.4167": [0, 0, -23.54], "1.5": [0, 0, -22.5], "1.5833": [0, 0, -21.46], "1.6667": [0, 0, -20.5], "1.75": [0, 0, -19.67], "1.8333": [0, 0, -19.04], "1.9167": [0, 0, -18.64], "2.0": [0, 0, -18.5]}}, "right_ear": {"rotation": {"0.0": [0, 0, 18.5], "0.0833": [0, 0, 18.64], "0.1667": [0, 0, 19.04], "0.25": [0, 0, 19.67], "0.3333": [0, 0, 20.5], "0.4167": [0, 0, 21.46], "0.5": [0, 0, 22.5], "0.5833": [0, 0, 23.54], "0.6667": [0, 0, 24.5], "0.75": [0, 0, 25.33], "0.8333": [0, 0, 25.96], "0.9167": [0, 0, 26.36], "1.0": [0, 0, 26.5], "1.0833": [0, 0, 26.36], "1.1667": [0, 0, 25.96], "1.25": [0, 0, 25.33], "1.3333": [0, 0, 24.5], "1.4167": [0, 0, 23.54], "1.5": [0, 0, 22.5], "1.5833": [0, 0, 21.46], "1.6667": [0, 0, 20.5], "1.75": [0, 0, 19.67], "1.8333": [0, 0, 19.04], "1.9167": [0, 0, 18.64], "2.0": [0, 0, 18.5]}}, "torso": {"rotation": {"0.0": [2.5, 0, 0], "0.0833": [2.24, 0, 0], "0.1667": [2, 0, 0], "0.25": [1.79, 0, 0], "0.3333": [1.63, 0, 0], "0.4167": [1.53, 0, 0], "0.5": [1.5, 0, 0], "0.5833": [1.53, 0, 0], "0.6667": [1.63, 0, 0], "0.75": [1.79, 0, 0], "0.8333": [2, 0, 0], "0.9167": [2.24, 0, 0], "1.0": [2.5, 0, 0], "1.0833": [2.76, 0, 0], "1.1667": [3, 0, 0], "1.25": [3.21, 0, 0], "1.3333": [3.37, 0, 0], "1.4167": [3.47, 0, 0], "1.5": [3.5, 0, 0], "1.5833": [3.47, 0, 0], "1.6667": [3.37, 0, 0], "1.75": [3.21, 0, 0], "1.8333": [3, 0, 0], "1.9167": [2.76, 0, 0], "2.0": [2.5, 0, 0]}, "position": {"0.0": [0, 0.15, 0], "0.0833": [0, 0.27, 0], "0.1667": [0, 0.37, 0], "0.25": [0, 0.45, 0], "0.3333": [0, 0.49, 0], "0.4167": [0, 0.5, 0], "0.5": [0, 0.48, 0], "0.5833": [0, 0.42, 0], "0.6667": [0, 0.33, 0], "0.75": [0, 0.23, 0], "0.8333": [0, 0.1, 0], "0.9167": [0, -0.03, 0], "1.0": [0, -0.15, 0], "1.0833": [0, -0.27, 0], "1.1667": [0, -0.37, 0], "1.25": [0, -0.45, 0], "1.3333": [0, -0.49, 0], "1.4167": [0, -0.5, 0], "1.5": [0, -0.48, 0], "1.5833": [0, -0.42, 0], "1.6667": [0, -0.33, 0], "1.75": [0, -0.23, 0], "1.8333": [0, -0.1, 0], "1.9167": [0, 0.03, 0], "2.0": [0, 0.15, 0]}}, "left_arm": {"rotation": {"0.0": [-5, 0, -23], "0.0833": [-5, 0, -23.07], "0.1667": [-5, 0, -23.27], "0.25": [-5, 0, -23.59], "0.3333": [-5, 0, -24], "0.4167": [-5, 0, -24.48], "0.5": [-5, 0, -25], "0.5833": [-5, 0, -25.52], "0.6667": [-5, 0, -26], "0.75": [-5, 0, -26.41], "0.8333": [-5, 0, -26.73], "0.9167": [-5, 0, -26.93], "1.0": [-5, 0, -27], "1.0833": [-5, 0, -26.93], "1.1667": [-5, 0, -26.73], "1.25": [-5, 0, -26.41], "1.3333": [-5, 0, -26], "1.4167": [-5, 0, -25.52], "1.5": [-5, 0, -25], "1.5833": [-5, 0, -24.48], "1.6667": [-5, 0, -24], "1.75": [-5, 0, -23.59], "1.8333": [-5, 0, -23.27], "1.9167": [-5, 0, -23.07], "2.0": [-5, 0, -23]}}, "right_arm": {"rotation": {"0.0": [-42.19, 17.39, 16.25], "0.0833": [-42.19, 17.39, 16.32], "0.1667": [-42.19, 17.39, 16.52], "0.25": [-42.19, 17.39, 16.83], "0.3333": [-42.19, 17.39, 17.25], "0.4167": [-42.19, 17.39, 17.73], "0.5": [-42.19, 17.39, 18.25], "0.5833": [-42.19, 17.39, 18.77], "0.6667": [-42.19, 17.39, 19.25], "0.75": [-42.19, 17.39, 19.66], "0.8333": [-42.19, 17.39, 19.98], "0.9167": [-42.19, 17.39, 20.18], "1.0": [-42.19, 17.39, 20.25], "1.0833": [-42.19, 17.39, 20.18], "1.1667": [-42.19, 17.39, 19.98], "1.25": [-42.19, 17.39, 19.66], "1.3333": [-42.19, 17.39, 19.25], "1.4167": [-42.19, 17.39, 18.77], "1.5": [-42.19, 17.39, 18.25], "1.5833": [-42.19, 17.39, 17.73], "1.6667": [-42.19, 17.39, 17.25], "1.75": [-42.19, 17.39, 16.83], "1.8333": [-42.19, 17.39, 16.52], "1.9167": [-42.19, 17.39, 16.32], "2.0": [-42.19, 17.39, 16.25]}}, "left_leg": {"rotation": {"0.0": [0, -45, 0], "0.0833": [0, -45, 0], "0.1667": [0, -45, 0], "0.25": [0, -45, 0], "0.3333": [0, -45, 0], "0.4167": [0, -45, 0], "0.5": [0, -45, 0], "0.5833": [0, -45, 0], "0.6667": [0, -45, 0], "0.75": [0, -45, 0], "0.8333": [0, -45, 0], "0.9167": [0, -45, 0], "1.0": [0, -45, 0], "1.0833": [0, -45, 0], "1.1667": [0, -45, 0], "1.25": [0, -45, 0], "1.3333": [0, -45, 0], "1.4167": [0, -45, 0], "1.5": [0, -45, 0], "1.5833": [0, -45, 0], "1.6667": [0, -45, 0], "1.75": [0, -45, 0], "1.8333": [0, -45, 0], "1.9167": [0, -45, 0], "2.0": [0, -45, 0]}, "position": {"0.0": [1, 0, 1], "0.0833": [1, 0, 1], "0.1667": [1, 0, 1], "0.25": [1, 0, 1], "0.3333": [1, 0, 1], "0.4167": [1, 0, 1], "0.5": [1, 0, 1], "0.5833": [1, 0, 1], "0.6667": [1, 0, 1], "0.75": [1, 0, 1], "0.8333": [1, 0, 1], "0.9167": [1, 0, 1], "1.0": [1, 0, 1], "1.0833": [1, 0, 1], "1.1667": [1, 0, 1], "1.25": [1, 0, 1], "1.3333": [1, 0, 1], "1.4167": [1, 0, 1], "1.5": [1, 0, 1], "1.5833": [1, 0, 1], "1.6667": [1, 0, 1], "1.75": [1, 0, 1], "1.8333": [1, 0, 1], "1.9167": [1, 0, 1], "2.0": [1, 0, 1]}}, "right_leg": {"rotation": {"0.0": [0, 22.5, 0], "0.0833": [0, 22.5, 0], "0.1667": [0, 22.5, 0], "0.25": [0, 22.5, 0], "0.3333": [0, 22.5, 0], "0.4167": [0, 22.5, 0], "0.5": [0, 22.5, 0], "0.5833": [0, 22.5, 0], "0.6667": [0, 22.5, 0], "0.75": [0, 22.5, 0], "0.8333": [0, 22.5, 0], "0.9167": [0, 22.5, 0], "1.0": [0, 22.5, 0], "1.0833": [0, 22.5, 0], "1.1667": [0, 22.5, 0], "1.25": [0, 22.5, 0], "1.3333": [0, 22.5, 0], "1.4167": [0, 22.5, 0], "1.5": [0, 22.5, 0], "1.5833": [0, 22.5, 0], "1.6667": [0, 22.5, 0], "1.75": [0, 22.5, 0], "1.8333": [0, 22.5, 0], "1.9167": [0, 22.5, 0], "2.0": [0, 22.5, 0]}, "position": {"0.0": [-1, 0, -1.5], "0.0833": [-1, 0, -1.5], "0.1667": [-1, 0, -1.5], "0.25": [-1, 0, -1.5], "0.3333": [-1, 0, -1.5], "0.4167": [-1, 0, -1.5], "0.5": [-1, 0, -1.5], "0.5833": [-1, 0, -1.5], "0.6667": [-1, 0, -1.5], "0.75": [-1, 0, -1.5], "0.8333": [-1, 0, -1.5], "0.9167": [-1, 0, -1.5], "1.0": [-1, 0, -1.5], "1.0833": [-1, 0, -1.5], "1.1667": [-1, 0, -1.5], "1.25": [-1, 0, -1.5], "1.3333": [-1, 0, -1.5], "1.4167": [-1, 0, -1.5], "1.5": [-1, 0, -1.5], "1.5833": [-1, 0, -1.5], "1.6667": [-1, 0, -1.5], "1.75": [-1, 0, -1.5], "1.8333": [-1, 0, -1.5], "1.9167": [-1, 0, -1.5], "2.0": [-1, 0, -1.5]}}, "legs": {"position": {"0.0": [0, 0, 0], "0.0833": [0, 0.03, 0], "0.1667": [0, 0.05, 0], "0.25": [0, 0.07, 0], "0.3333": [0, 0.09, 0], "0.4167": [0, 0.1, 0], "0.5": [0, 0.1, 0], "0.5833": [0, 0.1, 0], "0.6667": [0, 0.09, 0], "0.75": [0, 0.07, 0], "0.8333": [0, 0.05, 0], "0.9167": [0, 0.03, 0], "1.0": [0, 0, 0], "1.0833": [0, -0.03, 0], "1.1667": [0, -0.05, 0], "1.25": [0, -0.07, 0], "1.3333": [0, -0.09, 0], "1.4167": [0, -0.1, 0], "1.5": [0, -0.1, 0], "1.5833": [0, -0.1, 0], "1.6667": [0, -0.09, 0], "1.75": [0, -0.07, 0], "1.8333": [0, -0.05, 0], "1.9167": [0, -0.03, 0], "2.0": [0, 0, 0]}}, "eyes": {"rotation": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0], "1.0833": [0, 0, 0], "1.1667": [0, 0, 0], "1.25": [0, 0, 0], "1.3333": [0, 0, 0], "1.4167": [0, 0, 0], "1.5": [0, 0, 0], "1.5833": [0, 0, 0], "1.6667": [0, 0, 0], "1.75": [0, 0, 0], "1.8333": [0, 0, 0], "1.9167": [0, 0, 0], "2.0": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0], "1.0833": [0, 0, 0], "1.1667": [0, 0, 0], "1.25": [0, 0, 0], "1.3333": [0, 0, 0], "1.4167": [0, 0, 0], "1.5": [0, 0, 0], "1.5833": [0, 0, 0], "1.6667": [0, 0, 0], "1.75": [0, 0, 0], "1.8333": [0, 0, 0], "1.9167": [0, 0, 0], "2.0": [0, 0, 0]}, "scale": {"0.0": [1, 1, 1], "0.0833": [1, 1, 1], "0.1667": [1, 1, 1], "0.25": [1, 1, 1], "0.3333": [1, 1, 1], "0.4167": [1, 1, 1], "0.5": [1, 1, 1], "0.5833": [1, 1, 1], "0.6667": [1, 1, 1], "0.75": [1, 1, 1], "0.8333": [1, 1, 1], "0.9167": [1, 1, 1], "1.0": [1, 1, 1], "1.0833": [1, 1, 1], "1.1667": [1, 1, 1], "1.25": [1, 1, 1], "1.3333": [1, 1, 1], "1.4167": [1, 1, 1], "1.5": [1, 1, 1], "1.5833": [1, 1, 1], "1.6667": [1, 1, 1], "1.75": [1, 1, 1], "1.8333": [1, 1, 1], "1.9167": [1, 1, 1], "2.0": [1, 1, 1]}}, "sword": {"rotation": {"0.0": [45, 0, 0], "0.0833": [45, 0, 0], "0.1667": [45, 0, 0], "0.25": [45, 0, 0], "0.3333": [45, 0, 0], "0.4167": [45, 0, 0], "0.5": [45, 0, 0], "0.5833": [45, 0, 0], "0.6667": [45, 0, 0], "0.75": [45, 0, 0], "0.8333": [45, 0, 0], "0.9167": [45, 0, 0], "1.0": [45, 0, 0], "1.0833": [45, 0, 0], "1.1667": [45, 0, 0], "1.25": [45, 0, 0], "1.3333": [45, 0, 0], "1.4167": [45, 0, 0], "1.5": [45, 0, 0], "1.5833": [45, 0, 0], "1.6667": [45, 0, 0], "1.75": [45, 0, 0], "1.8333": [45, 0, 0], "1.9167": [45, 0, 0], "2.0": [45, 0, 0]}, "position": {"0.0": [0, -3.5, 0], "0.0833": [0, -3.5, 0], "0.1667": [0, -3.5, 0], "0.25": [0, -3.5, 0], "0.3333": [0, -3.5, 0], "0.4167": [0, -3.5, 0], "0.5": [0, -3.5, 0], "0.5833": [0, -3.5, 0], "0.6667": [0, -3.5, 0], "0.75": [0, -3.5, 0], "0.8333": [0, -3.5, 0], "0.9167": [0, -3.5, 0], "1.0": [0, -3.5, 0], "1.0833": [0, -3.5, 0], "1.1667": [0, -3.5, 0], "1.25": [0, -3.5, 0], "1.3333": [0, -3.5, 0], "1.4167": [0, -3.5, 0], "1.5": [0, -3.5, 0], "1.5833": [0, -3.5, 0], "1.6667": [0, -3.5, 0], "1.75": [0, -3.5, 0], "1.8333": [0, -3.5, 0], "1.9167": [0, -3.5, 0], "2.0": [0, -3.5, 0]}}}}, "animation.better_piglin_brute.idle_2": {"loop": true, "animation_length": 12, "bones": {"head": {"rotation": {"0.0": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.5": {"post": [2.75806, 24.97457, 1.16524], "lerp_mode": "catmullrom"}, "1.75": {"post": [2.75806, 24.97457, 1.16524], "lerp_mode": "catmullrom"}, "2.25": {"post": [2.58807, -14.98539, -0.66963], "lerp_mode": "catmullrom"}, "4.0": {"post": [2.81797, -27.47162, -1.30078], "lerp_mode": "catmullrom"}, "5.0": {"post": [2.81797, -27.47162, -1.30078], "lerp_mode": "catmullrom"}, "5.4167": {"post": [2.16372, -44.90862, 1.07114], "lerp_mode": "catmullrom"}, "6.5": {"post": [1.569, -12.42454, 2.26139], "lerp_mode": "catmullrom"}, "7.8333": {"post": [4.02975, -2.31851, 2.53902], "lerp_mode": "catmullrom"}, "9.0": {"post": [1.60594, -17.42262, 2.11808], "lerp_mode": "catmullrom"}, "9.3333": {"post": [-3.38985, 34.95697, -5.48447], "lerp_mode": "catmullrom"}, "9.625": {"post": [-3.49983, 37.45246, -5.67062], "lerp_mode": "catmullrom"}, "12.0": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "12.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "left_ear": {"rotation": {"0.0": {"post": [0, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "0.125": {"post": [0, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "0.5": {"post": [7.5, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "0.7917": {"post": [-2.5, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "5.0": {"post": [0, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "5.25": {"post": [-10, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "5.5": {"post": [2.5, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "5.75": {"post": [0, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "9.0": {"post": [0, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "9.2917": {"post": [20, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "9.5": {"post": [-13.09418, 0, -20.06495], "lerp_mode": "catmullrom"}, "9.7083": {"post": [7.5, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "9.9583": {"post": [0, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "12.0": {"post": [0, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}}}, "right_ear": {"rotation": {"0.0": {"post": [0, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "0.125": {"post": [0, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "0.5": {"post": [-10, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "0.7917": {"post": [7.5, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "1.25": {"post": [0, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "5.0": {"post": [0, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "5.25": {"post": [10, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "5.5": {"post": [-5, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "5.75": {"post": [0, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "9.0": {"post": [0, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "9.2917": {"post": [-17.5, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "9.5": {"post": [-17, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "9.7083": {"post": [10, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "9.9583": {"post": [-10, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "10.1667": {"post": [0, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "12.0": {"post": [0, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}}}, "torso": {"rotation": {"0.0": {"post": ["2.5-math.sin((q.anim_time)*180)*1", 0, 0], "lerp_mode": "catmullrom"}, "0.2917": {"post": ["2.5-math.sin((q.anim_time)*180)*1", 0, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": ["2.5529-math.sin((q.anim_time)*180)*1", 14.99474, 0.40187], "lerp_mode": "catmullrom"}, "5.0": {"post": ["2.5529-math.sin((q.anim_time)*180)*1", 14.99474, 0.40187], "lerp_mode": "catmullrom"}, "6.5": {"post": ["2.8099-math.sin((q.anim_time)*180)*1", -24.92885, -2.16378], "lerp_mode": "catmullrom"}, "7.5": {"post": ["3.2084-math.sin((q.anim_time)*180)*1", -37.41182, -2.92918], "lerp_mode": "catmullrom"}, "7.8333": {"post": ["13.2084-math.sin((q.anim_time)*180)*1", -37.41182, -2.92918], "lerp_mode": "catmullrom"}, "9.125": {"post": ["8.2084-math.sin((q.anim_time)*180)*1", -37.41182, -2.92918], "lerp_mode": "catmullrom"}, "9.4583": {"post": ["2.5845-math.sin((q.anim_time)*180)*1", 12.47642, 0.7735], "lerp_mode": "catmullrom"}, "10.0": {"post": [2.19162, 17.19939, 0.997], "lerp_mode": "catmullrom"}, "11.0": {"post": ["2.5-math.sin((q.anim_time)*180)*1", 0, 0], "lerp_mode": "catmullrom"}, "12.0": {"post": ["2.5-math.sin((q.anim_time)*180)*1", 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, "math.sin((q.anim_time+0.1)*180)*0.5", 0], "lerp_mode": "catmullrom"}, "12.0": {"post": [0, "math.sin((q.anim_time+0.1)*180)*0.5", 0], "lerp_mode": "catmullrom"}}}, "left_arm": {"rotation": {"0.0": {"post": [-5, 0, "-25+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "0.5": {"post": [5, 0, "-25+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "0.9167": {"post": [-12.53742, 4.92385, "-25.8704+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "1.2917": {"post": [-5.03742, 4.92385, "-25.8704+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "5.0": {"post": [-5.03742, 4.92385, "-25.8704+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "5.0833": {"post": [-5.03742, 4.92385, "-25.8704+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "5.5": {"post": [-12.53742, 4.92385, "-25.8704+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "6.3333": {"post": [2.46258, 4.92385, "-25.8704+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "7.5": {"post": [2.46258, 4.92385, "-25.8704+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "7.9583": {"post": [19.96258, 4.92385, "-25.8704+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "8.5": {"post": [19.96258, 4.92385, "-25.8704+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "9.0": {"post": [2.46258, 4.92385, "-25.8704+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "9.1667": {"post": [2.46258, 4.92385, "-25.8704+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "9.4167": {"post": [24.96258, 4.92385, "-25.8704+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "9.7083": {"post": [-12.53742, 4.92385, "-25.8704+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "9.9583": {"post": [-22.53742, 4.92385, "-25.8704+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "10.5417": {"post": [-12.53742, 4.92385, "-25.8704+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "12.0": {"post": [-5, 0, "-25+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}}}, "right_arm": {"rotation": {"0.0": {"post": [-42.18626, 17.38772, "18.2489-math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "0.7917": {"post": [-12.12763, 28.25095, "26.4782-math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "5.0": {"post": [-12.12763, 28.25095, "26.4782-math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "5.9167": {"post": [-74.86239, 11.3167, "15.5853-math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "7.5": {"post": [-74.86239, 11.3167, "15.5853-math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "7.7917": {"post": [-107.36239, 11.3167, "15.5853-math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "9.0": {"post": [-107.36239, 11.3167, "15.5853-math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "9.25": {"post": [-84.95114, 3.84622, "14.9141-math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "9.4583": {"post": [-113.13749, 60.34198, "3.1383-math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "10.0": {"post": [-97.00823, 84.40814, "14.9685-math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "12.0": {"post": [-42.18626, 17.38772, "18.2489-math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}}}, "left_leg": {"rotation": {"0.0": {"post": [0, -45, 0], "lerp_mode": "catmullrom"}, "0.625": {"post": [0, -45, 0], "lerp_mode": "catmullrom"}, "0.75": {"post": [0, -20.01734, -1.12419], "lerp_mode": "catmullrom"}, "5.0": {"post": [0, -20.01734, -1.12419], "lerp_mode": "catmullrom"}, "5.1667": {"post": [0, -35.00817, -0.52986], "lerp_mode": "catmullrom"}, "5.3333": {"post": [0, -49.99477, 0.33883], "lerp_mode": "catmullrom"}, "9.375": {"post": [0, -49.99477, 0.33883], "lerp_mode": "catmullrom"}, "9.5": {"post": [0, -27.51307, -0.84739], "lerp_mode": "catmullrom"}, "11.125": {"post": [0, -27.51307, -0.84739], "lerp_mode": "catmullrom"}, "12.0": {"post": [0, -45, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [1, 0, 1], "lerp_mode": "catmullrom"}, "0.5": {"post": [1, 0, 1], "lerp_mode": "catmullrom"}, "0.625": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "0.75": {"post": [1, 1, -1], "lerp_mode": "catmullrom"}, "0.875": {"post": [1, 0, -1], "lerp_mode": "catmullrom"}, "5.0": {"post": [1, 0, -1], "lerp_mode": "catmullrom"}, "5.3333": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "5.4583": {"post": [1, -0.12, 1.24], "lerp_mode": "catmullrom"}, "6.75": {"post": [1, 0, 2], "lerp_mode": "catmullrom"}, "9.375": {"post": [1, 0, 2], "lerp_mode": "catmullrom"}, "9.5": {"post": [1, 0.7, 0.9], "lerp_mode": "catmullrom"}, "9.625": {"post": [1, 0.7, -1.35], "lerp_mode": "catmullrom"}, "9.7083": {"post": [1, 0, -1.35], "lerp_mode": "catmullrom"}, "11.125": {"post": [1, 0, -1.35], "lerp_mode": "catmullrom"}, "11.25": {"post": [1, 0.73, -1.12], "lerp_mode": "catmullrom"}, "11.375": {"post": [1, 0.7, 0.15], "lerp_mode": "catmullrom"}, "11.5": {"post": [1, 0, 1], "lerp_mode": "catmullrom"}, "12.0": {"post": [1, 0, 1], "lerp_mode": "catmullrom"}}}, "right_leg": {"rotation": {"0.0": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "5.0": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "5.5417": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "5.6667": {"post": [0, 12.5, 0], "lerp_mode": "catmullrom"}, "5.7917": {"post": [0, -5, 0], "lerp_mode": "catmullrom"}, "9.0417": {"post": [0, -5, 0], "lerp_mode": "catmullrom"}, "9.1667": {"post": [0, 17.5, 0], "lerp_mode": "catmullrom"}, "10.75": {"post": [0, 17.5, 0], "lerp_mode": "catmullrom"}, "12.0": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [-1, 0, -1.5], "lerp_mode": "catmullrom"}, "0.125": {"post": [-1, 0, -1.5], "lerp_mode": "catmullrom"}, "0.25": {"post": [-1, 1, -1.5], "lerp_mode": "catmullrom"}, "0.375": {"post": [-1, 1, 0.5], "lerp_mode": "catmullrom"}, "0.5": {"post": [-1, 0, 1.5], "lerp_mode": "catmullrom"}, "5.0": {"post": [-1, 0, 1.5], "lerp_mode": "catmullrom"}, "5.5417": {"post": [-1, 0, 1.5], "lerp_mode": "catmullrom"}, "5.6667": {"post": [-1, 1, -0.5], "lerp_mode": "catmullrom"}, "5.7917": {"post": [-1, 1, -1.5], "lerp_mode": "catmullrom"}, "5.9583": {"post": [-1, 0, -1.5], "lerp_mode": "catmullrom"}, "9.0417": {"post": [-1, 0, -1.5], "lerp_mode": "catmullrom"}, "9.1667": {"post": [-1, 1, -2.5], "lerp_mode": "catmullrom"}, "9.2917": {"post": [-1, 0.99, -0.49], "lerp_mode": "catmullrom"}, "9.4167": {"post": [-1, 0, 1.03], "lerp_mode": "catmullrom"}, "10.75": {"post": [-1, 0, 1.03], "lerp_mode": "catmullrom"}, "10.875": {"post": [-1, 0.96, 0.52], "lerp_mode": "catmullrom"}, "11.0": {"post": [-1, 1, -1.5], "lerp_mode": "catmullrom"}, "11.125": {"post": [-1, 0, -1.65], "lerp_mode": "catmullrom"}, "12.0": {"post": [-1, 0, -1.5], "lerp_mode": "catmullrom"}}}, "legs": {"position": {"0.0": {"post": [0, "math.sin((q.anim_time)*180)*0.1", 0], "lerp_mode": "catmullrom"}, "5.0": {"post": [0, "math.sin((q.anim_time)*180)*0.1", 0], "lerp_mode": "catmullrom"}, "12.0": {"post": [0, "math.sin((q.anim_time)*180)*0.1", 0], "lerp_mode": "catmullrom"}}}, "eyes": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "5.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "5.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "12.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "5.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "5.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "12.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "scale": {"0.0": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "0.5": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "0.625": {"post": [1, 0, 1], "lerp_mode": "catmullrom"}, "0.7917": {"post": [1, 0.7, 1], "lerp_mode": "catmullrom"}, "1.75": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "1.875": {"post": [1, 0, 1], "lerp_mode": "catmullrom"}, "2.0417": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "2.125": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "2.25": {"post": [1, 0, 1], "lerp_mode": "catmullrom"}, "2.4167": {"post": [1, 0.7, 1], "lerp_mode": "catmullrom"}, "3.8333": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "3.9583": {"post": [1, 0, 1], "lerp_mode": "catmullrom"}, "4.125": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "5.0": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "5.5": {"post": [1, 0.5, 1], "lerp_mode": "catmullrom"}, "7.2083": {"post": [1, 0.7, 1], "lerp_mode": "catmullrom"}, "8.0": {"post": [1, 0.4, 1], "lerp_mode": "catmullrom"}, "9.0": {"post": [1, 0.7, 1], "lerp_mode": "catmullrom"}, "12.0": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}}}, "sword": {"rotation": [45, 0, 0], "position": [0, -3.5, 0]}}}, "animation.better_piglin_brute.idle_3": {"loop": true, "animation_length": 16, "bones": {"head": {"rotation": {"0.0": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "0.25": {"post": [12.31594, 2.15393, -9.76758], "lerp_mode": "catmullrom"}, "0.4167": {"post": [5.03417, 11.91141, -7.55939], "lerp_mode": "catmullrom"}, "0.625": {"post": [1.68555, 0.85721, 1.65318], "lerp_mode": "catmullrom"}, "0.8333": {"post": [1.68702, 5.44257, 6.95559], "lerp_mode": "catmullrom"}, "1.0417": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "2.2917": {"post": [-9.939, -0.30061, -0.38417], "lerp_mode": "catmullrom"}, "4.0": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}, "10.0": {"post": [32.5, 0, 0], "lerp_mode": "catmullrom"}, "12.0": {"post": [32.5, 0, 0], "lerp_mode": "catmullrom"}, "12.25": {"post": [-12.5, 0, 0], "lerp_mode": "catmullrom"}, "12.4167": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "12.6667": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "12.9167": {"post": [0, 27.5, 0], "lerp_mode": "catmullrom"}, "13.25": {"post": [0, 27.5, 0], "lerp_mode": "catmullrom"}, "13.3333": {"post": [0, 27.5, 0], "lerp_mode": "catmullrom"}, "13.6667": {"post": [0, -35, 0], "lerp_mode": "catmullrom"}, "14.25": {"post": [0, -40, 0], "lerp_mode": "catmullrom"}, "14.8333": {"post": [0, -40, 0], "lerp_mode": "catmullrom"}, "15.5": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "15.5": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "left_ear": {"rotation": {"0.0": {"post": [0, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, 0, "-22.5+math.cos((q.anim_time)*90)*4"], "lerp_mode": "catmullrom"}, "10.0": {"post": [-34.45567, -2.20478, -12.61789], "lerp_mode": "catmullrom"}, "12.0": {"post": [-34.45567, -2.20478, -12.61789], "lerp_mode": "catmullrom"}, "12.25": {"post": [-34.45567, -2.20478, -12.61789], "lerp_mode": "catmullrom"}, "12.4167": {"post": [32.9568, -2.21038, -12.59541], "lerp_mode": "catmullrom"}, "12.6667": {"post": [-4.51362, -2.22417, -12.52088], "lerp_mode": "catmullrom"}, "13.0": {"post": [0, 0, "-67.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "13.3333": {"post": [0, 0, "-45+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "13.6667": {"post": [-1.02771, -0.8188, -66.02188], "lerp_mode": "catmullrom"}, "14.25": {"post": [-0.95244, -0.90525, -71.0217], "lerp_mode": "catmullrom"}, "14.5": {"post": [-1.02771, -0.8188, -66.02188], "lerp_mode": "catmullrom"}, "15.5": {"post": [0, 0, "-22.5+math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}}}, "right_ear": {"rotation": {"0.0": {"post": [0, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, 0, "22.5-math.cos((q.anim_time)*90)*4"], "lerp_mode": "catmullrom"}, "10.0": {"post": [-34.45567, 2.20478, 12.61789], "lerp_mode": "catmullrom"}, "12.0": {"post": [-34.45567, 2.20478, 12.61789], "lerp_mode": "catmullrom"}, "12.25": {"post": [-34.45567, 2.20478, 12.61789], "lerp_mode": "catmullrom"}, "12.4167": {"post": [32.9568, 2.21038, 12.59541], "lerp_mode": "catmullrom"}, "12.6667": {"post": [-4.51362, 2.22417, 12.52088], "lerp_mode": "catmullrom"}, "13.0": {"post": [0, 0, "67.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "13.3333": {"post": [0, 0, "45-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}, "13.6667": {"post": [7.38926, 2.81441, 64.41338], "lerp_mode": "catmullrom"}, "14.25": {"post": [7.60479, 2.16163, 69.37529], "lerp_mode": "catmullrom"}, "14.5": {"post": [7.38926, 2.81441, 64.41338], "lerp_mode": "catmullrom"}, "15.5": {"post": [0, 0, "22.5-math.cos((q.anim_time)*180)*4"], "lerp_mode": "catmullrom"}}}, "torso": {"rotation": {"0.0": {"post": ["2.5-math.sin((q.anim_time)*180)*1", 0, 0], "lerp_mode": "catmullrom"}, "0.2083": {"post": [1.89804, -0.08286, 2.49863], "lerp_mode": "catmullrom"}, "0.375": {"post": [1.89854, -0.16559, 4.9906], "lerp_mode": "catmullrom"}, "0.5833": {"post": [1.89154, 0.16881, -5.10751], "lerp_mode": "catmullrom"}, "0.75": {"post": ["2.5-math.sin((q.anim_time)*180)*1", 0, 0], "lerp_mode": "catmullrom"}, "1.3333": {"post": ["-5-math.sin((q.anim_time)*180)*1", 0, 0], "lerp_mode": "catmullrom"}, "4.0": {"post": ["2.5-math.sin((q.anim_time)*180)*1", 0, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": ["2.5-math.sin((q.anim_time)*180)*1", 0, 0], "lerp_mode": "catmullrom"}, "10.0": {"post": ["7.5-math.sin((q.anim_time)*180)*1", 0, 0], "lerp_mode": "catmullrom"}, "12.0": {"post": ["7.5-math.sin((q.anim_time)*90)*1", 0, 0], "lerp_mode": "catmullrom"}, "12.1667": {"post": ["10-math.sin((q.anim_time)*90)*1", 0, 0], "lerp_mode": "catmullrom"}, "12.25": {"post": ["2.5-math.sin((q.anim_time)*90)*1", 0, 0], "lerp_mode": "catmullrom"}, "12.75": {"post": ["2.5-math.sin((q.anim_time)*90)*1", 0, 0], "lerp_mode": "catmullrom"}, "13.0": {"post": ["2.5129-math.sin((q.anim_time)*90)*1", 7.49742, 0.19745], "lerp_mode": "catmullrom"}, "13.2917": {"post": ["2.5129-math.sin((q.anim_time)*90)*1", 7.49742, 0.19745], "lerp_mode": "catmullrom"}, "13.5417": {"post": ["2.5183-math.sin((q.anim_time)*90)*1", -7.49485, -0.27868], "lerp_mode": "catmullrom"}, "14.8333": {"post": ["2.5183-math.sin((q.anim_time)*90)*1", -7.49485, -0.27868], "lerp_mode": "catmullrom"}, "15.5": {"post": ["2.5-math.sin((q.anim_time)*180)*1", 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, "math.sin((q.anim_time+0.1)*180)*0.5", 0], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, "math.sin((q.anim_time+0.1)*180)*0.5", 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, "math.sin((q.anim_time+0.1)*90)*0.5", 0], "lerp_mode": "catmullrom"}, "12.0": {"post": [0, "math.sin((q.anim_time+0.1)*90)*0.5", 0], "lerp_mode": "catmullrom"}, "12.1667": {"post": [0, "3+math.sin((q.anim_time+0.1)*90)*0.5", 0], "lerp_mode": "catmullrom"}, "12.3333": {"post": [0, "math.sin((q.anim_time+0.1)*90)*0.5", 0], "lerp_mode": "catmullrom"}, "14.8333": {"post": [0, "math.sin((q.anim_time+0.1)*90)*0.5", 0], "lerp_mode": "catmullrom"}, "15.5": {"post": [0, "math.sin((q.anim_time+0.1)*180)*0.5", 0], "lerp_mode": "catmullrom"}}}, "left_arm": {"rotation": {"0.0": {"post": [-5, 0, "-25+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "0.5833": {"post": [-5, 0, "-25+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "1.3333": {"post": ["-103.4034+math.sin((q.anim_time)*180)*3", 39.21042, -2.534], "lerp_mode": "catmullrom"}, "1.9167": {"post": ["-103.4034+math.sin((q.anim_time)*720)*5", 39.21042, -2.534], "lerp_mode": "catmullrom"}, "3.0": {"post": ["-103.4034+math.sin((q.anim_time)*540)*2", 39.21042, -2.534], "lerp_mode": "catmullrom"}, "4.0": {"post": [-5, 0, "-25+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "8.0": {"post": [-5, 0, "-25+math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "10.0": {"post": [-4.62111, 1.91134, "-2.5771+math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "12.0": {"post": [-4.62111, 1.91134, "-2.5771+math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "12.1667": {"post": [-4.83045, -1.29256, "-39.9455+math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "12.25": {"post": [-4.83045, -1.29256, "-39.9455+math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "12.375": {"post": [-4.83045, 1.29256, "-10.0545+math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "13.0": {"post": [0.01617, -0.42297, "-29.8402+math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "13.2917": {"post": [0.01617, -0.42297, "-29.8402+math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "13.5417": {"post": [-14.98383, -0.42297, "-29.8402+math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "14.8333": {"post": [-14.98383, -0.42297, "-29.8402+math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "15.5": {"post": [-5, 0, "-25+math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}}}, "right_arm": {"rotation": {"0.0": {"post": [-42.18626, 17.38772, "18.2489-math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "0.5833": {"post": [-42.18626, 17.38772, "18.2489-math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "1.6667": {"post": [15.90371, 14.78947, "26.3947-math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "4.0": {"post": [-42.18626, 17.38772, "18.2489-math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}, "8.0": {"post": [5.31374, 17.38772, "18.2489-math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "10.0": {"post": [1.27098, 13.11359, "5.0268-math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "12.0": {"post": [1.27098, 13.11359, "5.0268-math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "12.1667": {"post": [2.88042, 1.71762, "42.3152-math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "12.25": {"post": [2.88042, 1.71762, "42.3152-math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "12.375": {"post": [1.5067, 2.99608, "9.8115-math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "13.0": {"post": [2.23099, 2.50409, "24.8208-math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "13.2917": {"post": [2.23099, 2.50409, "24.8208-math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "13.5417": {"post": [-4.1824, 27.94988, "65.7668-math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "14.8333": {"post": [-4.1824, 27.94988, "65.7668-math.cos((q.anim_time)*90)*2"], "lerp_mode": "catmullrom"}, "16.0": {"post": [-42.18626, 17.38772, "18.2489-math.cos((q.anim_time)*180)*2"], "lerp_mode": "catmullrom"}}}, "left_leg": {"rotation": {"0.0": {"post": [0, -45, 0], "lerp_mode": "catmullrom"}, "0.4167": {"post": [0, -45, 0], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, -45, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, -45, 0], "lerp_mode": "catmullrom"}, "12.0": {"post": [-5, -45, 0], "lerp_mode": "catmullrom"}, "12.125": {"post": [0, -45, 0], "lerp_mode": "catmullrom"}, "14.8333": {"post": [0, -45, 0], "lerp_mode": "catmullrom"}, "16.0": {"post": [0, -45, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [1, 0, 1], "lerp_mode": "catmullrom"}, "0.4167": {"post": [1, 0, 1], "lerp_mode": "catmullrom"}, "0.5833": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "0.75": {"post": [1, 0.99, 0], "lerp_mode": "catmullrom"}, "0.9167": {"post": [1, -0.03, 0], "lerp_mode": "catmullrom"}, "4.0": {"post": [1, -0.03, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [1, -0.03, 0], "lerp_mode": "catmullrom"}, "12.0": {"post": [1, -0.03, 0], "lerp_mode": "catmullrom"}, "12.125": {"post": [1, 0.97, 0], "lerp_mode": "catmullrom"}, "12.25": {"post": [1, -0.03, 0], "lerp_mode": "catmullrom"}, "14.8333": {"post": [1, -0.03, 0], "lerp_mode": "catmullrom"}, "16.0": {"post": [1, 0, 1], "lerp_mode": "catmullrom"}}}, "right_leg": {"rotation": {"0.0": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "12.0": {"post": [-5, 22.5, 0], "lerp_mode": "catmullrom"}, "12.125": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "14.8333": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}, "16.0": {"post": [0, 22.5, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [-1, 0, -1.5], "lerp_mode": "catmullrom"}, "0.2083": {"post": [-1, 1, -1.5], "lerp_mode": "catmullrom"}, "0.375": {"post": [-1, 0.99, 0.5], "lerp_mode": "catmullrom"}, "0.5833": {"post": [-1, -0.04, 0.49], "lerp_mode": "catmullrom"}, "4.0": {"post": [-1, -0.04, 0.49], "lerp_mode": "catmullrom"}, "8.0": {"post": [-1, -0.04, 0.49], "lerp_mode": "catmullrom"}, "12.0": {"post": [-1, -0.04, 0.49], "lerp_mode": "catmullrom"}, "12.125": {"post": [-1, 0.96, 0.49], "lerp_mode": "catmullrom"}, "12.25": {"post": [-1, -0.04, 0.49], "lerp_mode": "catmullrom"}, "14.8333": {"post": [-1, -0.04, 0.49], "lerp_mode": "catmullrom"}, "16.0": {"post": [-1, 0, -1.5], "lerp_mode": "catmullrom"}}}, "legs": {"rotation": {"0.0": [0, 0, 0], "4.0": [0, 0, 0], "8.0": [0, 0, 0], "12.0": [0, 0, 0], "14.8333": [0, 0, 0], "16.0": [0, 0, 0]}, "position": {"0.0": {"post": [0, "math.sin((q.anim_time)*180)*0.1", 0], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, "math.sin((q.anim_time)*180)*0.1", 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, "math.sin((q.anim_time)*90)*0.1", 0], "lerp_mode": "catmullrom"}, "12.0": {"post": [0, "math.sin((q.anim_time)*90)*0.1", 0], "lerp_mode": "catmullrom"}, "14.8333": {"post": [0, "math.sin((q.anim_time)*90)*0.1", 0], "lerp_mode": "catmullrom"}, "16.0": {"post": [0, "math.sin((q.anim_time)*180)*0.1", 0], "lerp_mode": "catmullrom"}}}, "eyes": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "12.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "14.8333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "16.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "4.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "8.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "12.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "14.8333": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}, "16.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "scale": {"0.0": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "1.0": {"post": [1, 0.3211, 1], "lerp_mode": "catmullrom"}, "2.0": {"post": [1, 0.3, 1], "lerp_mode": "catmullrom"}, "3.375": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "3.5": {"post": [1, 0.1, 1], "lerp_mode": "catmullrom"}, "3.625": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "4.0": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "4.25": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "4.375": {"post": [1, 0.1, 1], "lerp_mode": "catmullrom"}, "4.5": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "4.6667": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "4.7917": {"post": [1, 0.1, 1], "lerp_mode": "catmullrom"}, "4.9167": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}, "5.7917": {"post": [1, 0.1, 1], "lerp_mode": "catmullrom"}, "6.1667": {"post": [1, 1.3, 1], "lerp_mode": "catmullrom"}, "8.0": {"post": [1, 0.1, 1], "lerp_mode": "catmullrom"}, "12.0": {"post": [1, 0.1, 1], "lerp_mode": "catmullrom"}, "12.1667": {"post": [1, 2.1, 1], "lerp_mode": "catmullrom"}, "12.3333": {"post": [1, 2.1, 1], "lerp_mode": "catmullrom"}, "12.5": {"post": [1, 0.1, 1], "lerp_mode": "catmullrom"}, "12.625": {"post": [1, 2.1, 1], "lerp_mode": "catmullrom"}, "12.875": {"post": [1, 2.1, 1], "lerp_mode": "catmullrom"}, "13.0": {"post": [1, 0.1, 1], "lerp_mode": "catmullrom"}, "13.125": {"post": [1, 2.1, 1], "lerp_mode": "catmullrom"}, "13.6667": {"post": [1, 2.1, 1], "lerp_mode": "catmullrom"}, "13.7917": {"post": [1, 0.1, 1], "lerp_mode": "catmullrom"}, "13.9167": {"post": [1, 2.1, 1], "lerp_mode": "catmullrom"}, "14.0417": {"post": [1, 2.1, 1], "lerp_mode": "catmullrom"}, "14.1667": {"post": [1, 0.1, 1], "lerp_mode": "catmullrom"}, "14.2917": {"post": [1, 2.1, 1], "lerp_mode": "catmullrom"}, "14.8333": {"post": [1, 2.1, 1], "lerp_mode": "catmullrom"}, "16.0": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}}}, "sword": {"rotation": [45, 0, 0], "position": [0, -3.5, 0]}}}, "animation.better_piglin_brute.attack_1": {"loop": true, "animation_length": 0.6667, "bones": {"head": {"rotation": {"0.0": [2.5, 0, 0], "0.0833": [2.5, 0, 0], "0.1667": [10.01, -5, -0.22], "0.25": [10.01, -5, -0.22], "0.3333": [-13.1, 7.73, -0.57], "0.4167": [-13.18, 25.33, -2.19], "0.5": [4.11, 27.32, 4.11], "0.5833": [2.83, 10.15, -2.21], "0.6667": [2.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0]}}, "left_ear": {"rotation": {"0.0": [0, 0, -22.5], "0.0833": [-27.5, 0, -22.5], "0.1667": [-24.22, 0, -22.5], "0.25": [-12.5, 0, -22.5], "0.3333": [27.5, 0, -22.5], "0.375": [17.5, 0, -22.5], "0.4167": [-7.34, 0, -22.5], "0.4583": [-32.5, 0, -22.5], "0.5": [-45, 0, -22.5], "0.5833": [5, 0, -22.5], "0.6667": [0, 0, -22.5]}}, "right_ear": {"rotation": {"0.0": [0, 0, 22.5], "0.0833": [-27.5, 0, 22.5], "0.1667": [-24.22, 0, 22.5], "0.25": [-12.5, 0, 22.5], "0.3333": [27.5, 0, 22.5], "0.375": [17.5, 0, 22.5], "0.4167": [-7.34, 0, 22.5], "0.4583": [-32.5, 0, 22.5], "0.5": [-45, 0, 22.5], "0.5833": [5, 0, 22.5], "0.6667": [0, 0, 22.5]}}, "torso": {"rotation": {"0.0": [2.5, 0, 0], "0.0833": [7.5, 0, 0], "0.1667": [2.53, 4.96, 0.65], "0.25": [0.03, 4.96, 0.65], "0.3333": [21.01, -8.45, 0.48], "0.4167": [28.92, -26.24, -9.15], "0.5": [13.92, -26.24, -9.15], "0.5833": [6.79, -10.25, 0.01], "0.6667": [2.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0]}}, "left_arm": {"rotation": {"0.0": [-5, 0, -25], "0.0833": [-52.5, 0, -25], "0.1667": [-75.12, 7.09, -12.78], "0.25": [-69.09, 23.14, 3.28], "0.3333": [-105.9, -35.16, 18.23], "0.4167": [-93.37, -44.72, 22.07], "0.5": [-81.64, -54.7, 23.08], "0.5833": [-39.62, -27.97, -0.9], "0.6667": [-5, 0, -25]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, -1, 3], "0.4167": [0, -0.75, 2.25], "0.5": [0, -0.5, 1.5], "0.5833": [0, -0.25, 0.75], "0.6667": [0, 0, 0]}}, "right_arm": {"rotation": {"0.0": [-42.19, 17.39, 18.25], "0.0833": [-77.38, 12.09, 1.83], "0.1667": [-164.17, 7.21, -15.72], "0.25": [-211.01, -13.22, -33.19], "0.3333": [-48.19, 2.32, -61.96], "0.4167": [-38.19, 2.32, -61.96], "0.5": [-42.19, 17.39, 18.25]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, -3], "0.4167": [0, 0, -1.5], "0.5": [0, 0, 0]}}, "left_leg": {"rotation": {"0.0": [0, -45, 0], "0.0833": [0, -42.5, 0], "0.1667": [0, -42.5, 0], "0.25": [0, -45, 0], "0.3333": [0, -60.83, 0], "0.375": [0, -67.5, 0], "0.4167": [0, -69.3, 0], "0.5": [0, -70.2, 0], "0.5833": [0, -67.5, 0], "0.6667": [0, -45, 0]}, "position": {"0.0": [1, 0, 1], "0.0833": [1, 0, 1], "0.1667": [1, 0, 1], "0.25": [1, 0, 1], "0.3333": [1, 0, 1], "0.4167": [1, 0, 1], "0.5": [1, 0, 1], "0.5833": [1, 0, 1], "0.6667": [1, 0, 1]}}, "right_leg": {"rotation": {"0.0": [0, 22.5, 0], "0.0833": [0, 25, 0], "0.1667": [0, 22.5, 0], "0.25": [0, 2.5, 0], "0.3333": [0, 0.28, 0], "0.4167": [0, 0.28, 0], "0.5": [0, 2.5, 0], "0.5833": [0, 12.5, 0], "0.6667": [0, 22.5, 0]}, "position": {"0.0": [-1, 0, -1.5], "0.0833": [-1, 0, -1.5], "0.1667": [-1, 1, -2.5], "0.25": [-1, 0, -3.5], "0.3333": [-1, -0.11, -3.61], "0.4167": [-1, -0.11, -3.61], "0.5": [-1, 0, -3.5], "0.5833": [-1, 1, -2.5], "0.6667": [-1, 0, -1.5]}}, "legs": {"position": [0, 0, 0]}, "eyes": {"rotation": [0, 0, 0], "position": [0, 0, 0], "scale": {"0.0": [1, 1, 1], "0.0833": [1, 0.4, 1], "0.1667": [1, 0.3259, 1], "0.25": [1, 0.3074, 1], "0.3333": [1, 0.3, 1], "0.4167": [1, 0.25, 1], "0.5": [1, 0.3, 1], "0.5833": [1, 0.6875, 1], "0.6667": [1, 1, 1]}}, "sword": {"rotation": {"0.0": [45, 0, 0], "0.0833": [37.5, 0, 0], "0.1667": [37.5, 0, 0], "0.25": [45, 0, 0], "0.3333": [112.5, 0, 0], "0.4167": [120.94, 0, 0], "0.5": [112.5, 0, 0], "0.5833": [78.75, 0, 0], "0.6667": [45, 0, 0]}, "position": {"0.0": [0, -3.5, 0], "0.0833": [0, -3.5, 0], "0.1667": [0, -3.5, 0], "0.25": [0, -3.5, 0], "0.3333": [0, -3.5, 0], "0.4167": [0, -3.5, 0], "0.5": [0, -3.5, 0], "0.5833": [0, -3.5, 0], "0.6667": [0, -3.5, 0]}}}}, "animation.better_piglin_brute.attack_2": {"loop": true, "animation_length": 0.75, "bones": {"head": {"rotation": {"0.0": [2.5, 0, 0], "0.0833": [5.26, 5.75, -0.14], "0.1667": [8.09, 10.92, 0.29], "0.25": [10.09, 14.99, 0.67], "0.3333": [11.51, 20.93, 0.61], "0.4167": [10.16, 19.98, 0.91], "0.5": [-4.38, -20.12, 4.41], "0.5833": [-3.7, -17.69, 3.33], "0.6667": [-0.64, -8.56, 1.39], "0.75": [2.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0]}}, "left_ear": {"rotation": {"0.0": [0, 0, -22.5], "0.0833": [-15, 0, -22.5], "0.1667": [-14.05, -0.21, -22.01], "0.25": [-11.21, -0.55, -21.2], "0.3333": [-6.52, -0.62, -21.04], "0.4167": [0, 0, -22.5], "0.5": [44.44, 8.8, -43.29], "0.5833": [49.44, 8.8, -43.29], "0.6667": [25.97, 4.4, -32.9], "0.75": [0, 0, -22.5]}}, "right_ear": {"rotation": {"0.0": [0, 0, 22.5], "0.0833": [-15, 0, 22.5], "0.1667": [-14.05, 0.21, 22.01], "0.25": [-11.21, 0.55, 21.2], "0.3333": [-6.52, 0.62, 21.04], "0.4167": [0, 0, 22.5], "0.5": [44.44, -8.8, 43.29], "0.5833": [49.44, -8.8, 43.29], "0.6667": [25.97, -4.4, 32.9], "0.75": [0, 0, 22.5]}}, "torso": {"rotation": {"0.0": [2.5, 0, 0], "0.0833": [2.78, 0.46, 0.05], "0.125": [2.5, 0, 0], "0.1667": [0.39, -4.26, -0.19], "0.25": [-4.91, -14.99, -0.67], "0.3333": [-7.67, -20.77, -0.89], "0.4167": [-7.34, -19.98, -0.91], "0.5": [9.89, 17.55, 0.11], "0.5833": [8.97, 15.13, 0.15], "0.6667": [5.33, 6.59, 0.07], "0.75": [2.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0.11, 0], "0.125": [0, 0, 0], "0.1667": [0, -0.11, 0], "0.25": [0, -0.46, 0], "0.3333": [0, -0.83, 0], "0.4167": [0, -1, 0], "0.5": [0, -0.87, 0], "0.5833": [0, -0.56, 0], "0.6667": [0, -0.23, 0], "0.75": [0, 0, 0]}}, "left_arm": {"rotation": {"0.0": [-5, 0, -25], "0.0833": [-12.8, -0.55, -26.42], "0.1667": [-34.87, -0.27, -25.71], "0.25": [-52.5, 0, -25], "0.3333": [-57.6, -0.46, -26.2], "0.4167": [-60, 0, -25], "0.5": [-85.89, 7.37, -5.83], "0.5833": [-62.08, 5.73, -10.09], "0.6667": [-26.41, 2.46, -18.61], "0.75": [-5, 0, -25]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, -0.33, 0], "0.1667": [0, -0.67, 0], "0.25": [0, -1, 0], "0.3333": [0, -1, 0.33], "0.4167": [0, -1, 0.67], "0.5": [0, -1, 1], "0.5833": [0, -0.67, 0.67], "0.6667": [0, -0.33, 0.33], "0.75": [0, 0, 0]}}, "right_arm": {"rotation": {"0.0": [-42.19, 17.39, 18.25], "0.0833": [-60.91, 5.51, 23.9], "0.1667": [-86.56, -12.46, 41.63], "0.25": [-105.28, -23.71, 55.89], "0.3333": [-113.56, -17.07, 59.72], "0.4167": [-105.91, -4.3, 62.24], "0.5": [-41.55, 3.59, 89.37], "0.5833": [-34.63, 9.79, 68.92], "0.6667": [-34.94, 16.64, 37.54], "0.75": [-42.19, 17.39, 18.25]}, "position": {"0.0": [0, 0, 0], "0.0833": [0.67, -1, -0.67], "0.1667": [1.33, -2, -1.33], "0.25": [2, -3, -2], "0.3333": [1.84, -2.83, -2], "0.4167": [1.67, -2.67, -2], "0.5": [0, -1, -2], "0.5833": [0, -0.67, -1.33], "0.6667": [0, -0.33, -0.67], "0.75": [0, 0, 0]}}, "left_leg": {"rotation": {"0.0": [0, -45, 0], "0.0833": [0, -45, 0], "0.1667": [0, -46.11, 0], "0.25": [0, -47.66, 0], "0.3333": [0, -49.75, 0], "0.4167": [0, -52.5, 0], "0.5": [0, -70, 0], "0.5833": [0, -63.89, 0], "0.6667": [0, -53.06, 0], "0.75": [0, -45, 0]}, "position": {"0.0": [1, 0, 1], "0.0833": [1, 0, 1], "0.1667": [1, 0, 1], "0.25": [1, 0, 1], "0.3333": [1, 0, 1], "0.4167": [1, 0, 1], "0.5": [1, 0, 1], "0.5833": [1, 0, 1], "0.6667": [1, 0, 1], "0.75": [1, 0, 1]}}, "right_leg": {"rotation": {"0.0": [0, 22.5, 0], "0.0833": [0, 0, 0], "0.1667": [0, -2.11, 0], "0.25": [0, -2.81, 0], "0.3333": [0, -2.11, 0], "0.4167": [0, 0, 0], "0.5": [0, 22.5, 0], "0.5833": [0, 25, 0], "0.6667": [0, 25, 0], "0.75": [0, 22.5, 0]}, "position": {"0.0": [-1, 0, -1.5], "0.0833": [-1, 1, -1.5], "0.1667": [-1, 1, -2.5], "0.25": [-1, 0, -2.5], "0.3333": [-1, -0.06, -2.62], "0.4167": [-1, 0, -2.5], "0.5": [-1, 0, -0.5], "0.5833": [-1, -0.04, -0.65], "0.6667": [-1, -0.07, -1.13], "0.75": [-1, 0, -1.5]}}, "legs": {"position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0]}}, "eyes": {"rotation": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0]}, "scale": {"0.0": [1, 1, 1], "0.0833": [1, 1, 1], "0.1667": [1, 0.7667, 1], "0.25": [1, 0.4556, 1], "0.3333": [1, 0.3, 1], "0.4167": [1, 0.3616, 1], "0.5": [1, 0.5128, 1], "0.5833": [1, 0.7032, 1], "0.6667": [1, 0.8824, 1], "0.75": [1, 1, 1]}}, "sword": {"rotation": {"0.0": [45, 0, 0], "0.0833": [45, 0, 0], "0.1667": [45, 0, 0], "0.25": [45, 0, 0], "0.3333": [45, 0, 0], "0.4167": [45, 0, 0], "0.5": [112.5, 0, 0], "0.5833": [90, 0, 0], "0.6667": [67.5, 0, 0], "0.75": [45, 0, 0]}, "position": {"0.0": [0, -3.5, 0], "0.0833": [0, -3.5, 0], "0.1667": [0, -3.5, 0], "0.25": [0, -3.5, 0], "0.3333": [0, -3.5, 0], "0.4167": [0, -3.5, 0], "0.5": [0, -3.5, 0], "0.5833": [0, -3.5, 0], "0.6667": [0, -3.5, 0], "0.75": [0, -3.5, 0]}, "scale": {"0.0": [1, 1, 1], "0.0833": [1, 1, 1], "0.1667": [1, 1, 1], "0.25": [1, 1, 1], "0.3333": [1, 1, 1], "0.4167": [1, 1, 1], "0.4583": [1, 1.7, 1.5], "0.5": [1, 1.35, 1.25], "0.5417": [1, 1, 1]}}}}, "animation.ptd_dbb_piglin_brute.spawn": {"loop": "hold_on_last_frame", "animation_length": 1, "bones": {"head": {"rotation": {"0.0": [30, 0, 0], "0.0833": [26.81, 0, 0], "0.1667": [22, 0, 0], "0.25": [16.63, 0, 0], "0.3333": [11.72, 0, 0], "0.4167": [8.32, 0, 0], "0.4583": [7.5, 0, 0], "0.5": [8.75, 0, 0], "0.5833": [17.81, 0, 0], "0.625": [20, 0, 0], "0.6667": [18.34, 0, 0], "0.75": [10.33, 0, 0], "0.7917": [7.5, 0, 0], "0.8333": [6.7, 0, 0], "0.9167": [6.9, 0, 0], "1.0": [7.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0]}}, "left_ear": {"rotation": {"0.0": [0, 0, -121], "0.0833": [0, 0, -142.74], "0.1667": [0, 0, -144.77], "0.25": [0, 0, -137.84], "0.3333": [0, 0, -143.26], "0.4167": [0, 0, -135], "0.5": [1.67, 0, -57.46], "0.5417": [0, 0, -22.5], "0.5833": [-7.5, 0, -9.35], "0.6667": [-22.5, 0, -7.5], "0.75": [0, 0, -17.5], "0.8333": [22.5, 0, -15], "0.9167": [12.66, 0, -18.59], "1.0": [0, 0, -22.5]}}, "right_ear": {"rotation": {"0.0": [0, 0, 121], "0.0833": [0, 0, 142.74], "0.1667": [0, 0, 144.77], "0.25": [0, 0, 137.84], "0.3333": [0, 0, 143.26], "0.4167": [0, 0, 135], "0.5": [1.67, 0, 57.46], "0.5417": [0, 0, 22.5], "0.5833": [-7.5, 0, 9.35], "0.6667": [-22.5, 0, 7.5], "0.75": [0, 0, 17.5], "0.8333": [22.5, 0, 15], "0.9167": [12.66, 0, 18.59], "1.0": [0, 0, 22.5]}}, "torso": {"rotation": {"0.0": [12.5, 0, 0], "0.0833": [12.81, 0, 0], "0.1667": [12.5, 0, 0], "0.25": [9.66, 0, 0], "0.3333": [6.98, 0, 0], "0.375": [7.5, 0, 0], "0.4167": [11.74, 0, 0], "0.5": [24.9, 0, 0], "0.5417": [27.5, 0, 0], "0.5833": [24.39, 0, 0], "0.6667": [10.21, 0, 0], "0.7083": [5, 0, 0], "0.75": [3.57, 0, 0], "0.8333": [2.4, 0, 0], "0.9167": [2.45, 0, 0], "1.0": [2.5, 0, 0]}, "position": {"0.0": [0, 14, 0], "0.0833": [0, 14.88, 0], "0.1667": [0, 14, 0], "0.25": [0, 8.83, 0], "0.3333": [0, 2.26, 0], "0.375": [0, 0, 0], "0.4167": [0, -1.7, 0], "0.5": [0, -2, 0], "0.5833": [0, -1.39, 0], "0.6667": [0, -0.34, 0], "0.7083": [0, 0, 0], "0.75": [0, 0.1, 0], "0.8333": [0, 0.14, 0], "0.9167": [0, 0.06, 0], "1.0": [0, 0, 0]}}, "left_arm": {"rotation": {"0.0": [-53.01, -38.33, -62.4], "0.0833": [-56.21, -36.63, -58.41], "0.125": [-53.01, -38.33, -62.4], "0.1667": [-45.68, -42.28, -71.53], "0.25": [-22.65, -54.72, -100.24], "0.3333": [-9.77, -61.38, -116.27], "0.4167": [-41.84, -45.19, -79.33], "0.4583": [-56.66, -33.62, -57.52], "0.5": [-58.99, -21.6, -43.54], "0.5833": [-47.5, 0, -25], "0.6667": [-40.45, 2.15, -22.92], "0.75": [-30.86, 2.42, -22.66], "0.8333": [-20.54, 1.61, -23.44], "0.9167": [-11.31, 0.54, -24.48], "1.0": [-5, 0, -25]}}, "right_arm": {"rotation": {"0.0": [-56.37, 67.76, 63.87], "0.0833": [-56.37, 67.76, 63.87], "0.1667": [-43.82, 72.82, 79.16], "0.25": [-26.93, 78.93, 98.98], "0.3333": [-17.17, 77.33, 104.67], "0.4167": [-33.32, 35.5, 45.37], "0.4583": [-42.19, 17.39, 18.25], "0.5": [-43.01, 15.42, 15.42], "0.5833": [-43.89, 13.3, 12.35], "0.6667": [-44.01, 13.02, 11.96], "0.75": [-43.62, 13.95, 13.29], "0.8333": [-43.01, 15.42, 15.42], "0.9167": [-42.44, 16.79, 17.38], "1.0": [-42.19, 17.39, 18.25]}}, "left_leg": {"rotation": {"0.0": [0, -45, 0], "0.0833": [0, -45, 0], "0.1667": [0, -45, 0], "0.2083": [0, -45, 0], "0.25": [-0.12, -45, 0], "0.3333": [-0.54, -45, 0], "0.4167": [0, -45, 0], "0.5": [5.83, -45, 0], "0.5417": [7.5, -45, 0], "0.5833": [5.83, -45, 0], "0.6667": [0, -45, 0], "0.75": [-0.53, -45, 0], "0.8333": [-0.47, -45, 0], "0.9167": [-0.18, -45, 0], "1.0": [0, -45, 0]}, "position": {"0.0": [1, 13.75, 1], "0.0833": [1, 14.41, 1], "0.1667": [1, 14.63, 1], "0.2083": [1, 13.75, 1], "0.25": [1, 11.66, 1.01], "0.3333": [0.98, 5.17, 1.05], "0.4167": [1, 0, 1], "0.5": [1.19, -0.51, 0.42], "0.5417": [1.25, 0, 0.25], "0.5833": [1.19, 0, 0.42], "0.6667": [1, 0, 1], "0.75": [0.98, 0, 1.05], "0.8333": [0.98, 0, 1.05], "0.9167": [0.99, 0, 1.02], "1.0": [1, 0, 1]}}, "right_leg": {"rotation": {"0.0": [0, 22.5, 0], "0.0833": [0, 22.5, 0], "0.125": [0, 22.5, 0], "0.1667": [-0.06, 22.5, 0], "0.25": [-0.31, 22.5, 0], "0.3333": [-0.29, 22.5, 0], "0.375": [0, 22.5, 0], "0.4167": [1.67, 22.5, 0], "0.5": [5, 22.5, 0], "0.5833": [1.67, 22.5, 0], "0.625": [0, 22.5, 0], "0.6667": [-0.22, 22.5, 0], "0.75": [-0.37, 22.5, 0], "0.8333": [-0.27, 22.5, 0], "0.9167": [-0.1, 22.5, 0], "1.0": [0, 22.5, 0]}, "position": {"0.0": [-1, 16, -1.5], "0.0833": [-1, 15.63, -1.5], "0.125": [-1, 14, -1.5], "0.1667": [-1, 12.2, -1.49], "0.25": [-1, 6.91, -1.47], "0.3333": [-1, 1.69, -1.47], "0.375": [-1, 0, -1.5], "0.4167": [-1, -1.2, -1.67], "0.5": [-1, -0.5, -2], "0.5833": [-1, -0.17, -1.67], "0.625": [-1, 0, -1.5], "0.6667": [-1, 0.02, -1.48], "0.75": [-1, 0.04, -1.46], "0.8333": [-1, 0.03, -1.47], "0.9167": [-1, 0.01, -1.49], "1.0": [-1, 0, -1.5]}}, "legs": {"position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0]}}, "eyes": {"rotation": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0]}, "scale": {"0.0": [1, 1, 1], "0.0833": [1, 1.0134, 1], "0.1667": [1, 1.0384, 1], "0.25": [1, 1.0519, 1], "0.3333": [1, 1.0307, 1], "0.375": [1, 1, 1], "0.4167": [1, 0.8824, 1], "0.5": [1, 0.5128, 1], "0.5833": [1, 0.3, 1], "0.6667": [1, 0.3616, 1], "0.75": [1, 0.5128, 1], "0.8333": [1, 0.7032, 1], "0.9167": [1, 0.8824, 1], "1.0": [1, 1, 1]}}, "sword": {"rotation": {"0.0": [45, 0, 0], "0.0833": [45, 0, 0], "0.1667": [45, 0, 0], "0.25": [45, 0, 0], "0.3333": [45, 0, 0], "0.4167": [45, 0, 0], "0.5": [45, 0, 0], "0.5833": [45, 0, 0], "0.6667": [45, 0, 0], "0.75": [45, 0, 0], "0.8333": [45, 0, 0], "0.9167": [45, 0, 0], "1.0": [45, 0, 0]}, "position": {"0.0": [0, -3.5, 0], "0.0833": [0, -3.5, 0], "0.1667": [0, -3.5, 0], "0.25": [0, -3.5, 0], "0.3333": [0, -3.5, 0], "0.4167": [0, -3.5, 0], "0.5": [0, -3.5, 0], "0.5833": [0, -3.5, 0], "0.6667": [0, -3.5, 0], "0.75": [0, -3.5, 0], "0.8333": [0, -3.5, 0], "0.9167": [0, -3.5, 0], "1.0": [0, -3.5, 0]}}, "root": {"position": {"0.0": [0, 12, 0], "0.0833": [0, 9.79, 0], "0.1667": [0, 6.52, 0], "0.25": [0, 2.82, 0], "0.3333": [0, -0.65, 0], "0.4167": [0, -3.24, 0], "0.4583": [0, -4, 0], "0.5": [0, -4, 0], "0.5833": [0, 0, 0]}, "scale": {"0.0": [0, 0, 0], "0.0417": [0, 0, 0], "0.0833": [0.15, 1.15, 0.3], "0.125": [0.3, 2.3, 0.6], "0.1667": [0.4016, 2.2801, 0.6829], "0.25": [0.6688, 1.8125, 0.8375], "0.3333": [0.9192, 1.2005, 0.9588], "0.375": [1, 1, 1], "0.4167": [1.0438, 0.75, 1.025], "0.4583": [1, 0.7, 1], "0.5": [1, 0.7667, 1], "0.5833": [1, 1, 1]}}}}, "animation.ptd_dbb_piglin_brute.walk": {"loop": true, "animation_length": 0.7083, "bones": {"head": {"rotation": {"0.0": [2.52, -7.6, 2.17], "0.0833": [2.52, -7.6, 2.17], "0.1667": [12.03, 2.4, -1.66], "0.25": [7.43, 6.91, -3.39], "0.3333": [1.65, 10.17, -4.63], "0.4167": [1.65, 10.17, -4.63], "0.5": [12.03, 2.4, -1.66], "0.5833": [9.2, -1.92, 0], "0.6667": [4.13, -6.21, 1.63], "0.7083": [2.52, -7.6, 2.17]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.7083": [0, 0, 0]}}, "left_ear": {"rotation": {"0.0": [0, 0, -15], "0.0833": [0, 0, -21.25], "0.1667": [0, 0, -27.5], "0.25": [0, 0, -21.25], "0.3333": [0, 0, -15], "0.4167": [0, 0, -21.25], "0.5": [0, 0, -27.5], "0.5833": [0, 0, -23.1], "0.6667": [0, 0, -16.3], "0.7083": [0, 0, -15]}}, "right_ear": {"rotation": {"0.0": [0, 0, 15], "0.0833": [0, 0, 21.25], "0.1667": [0, 0, 27.5], "0.25": [0, 0, 21.25], "0.3333": [0, 0, 15], "0.4167": [0, 0, 21.25], "0.5": [0, 0, 27.5], "0.5833": [0, 0, 23.1], "0.6667": [0, 0, 16.3], "0.7083": [0, 0, 15]}}, "torso": {"rotation": {"0.0": [2.52, 7.49, 0.33], "0.0833": [2.52, 4.76, 0.21], "0.1667": [2.53, -1.25, -0.06], "0.25": [2.54, -7.26, -0.32], "0.3333": [2.54, -9.99, -0.44], "0.4167": [2.54, -7.78, -0.34], "0.5": [2.53, -2.7, -0.12], "0.5833": [2.53, 2.96, 0.13], "0.6667": [2.52, 6.89, 0.3], "0.7083": [2.52, 7.49, 0.33]}, "position": {"0.0": [0, 1, 0], "0.0833": [0, 0.4, 0], "0.1667": [0, -0.23, 0], "0.25": [0, 0.25, 0], "0.3333": [0, 0.76, 0], "0.4167": [0, 0.25, 0], "0.5": [0, -0.23, 0], "0.5833": [0, 0.22, 0], "0.6667": [0, 0.88, 0], "0.7083": [0, 1, 0]}}, "left_arm": {"rotation": {"0.0": [-50, 0, -25], "0.0833": [-35.94, 0, -25], "0.1667": [-5, 0, -25], "0.25": [25.94, 0, -25], "0.3333": [40, 0, -25], "0.4167": [28.64, 0, -25], "0.5": [2.47, 0, -25], "0.5833": [-26.67, 0, -25], "0.6667": [-46.91, 0, -25], "0.7083": [-50, 0, -25]}}, "right_arm": {"rotation": {"0.0": [47.81, 17.39, 18.25], "0.0833": [33.75, 17.39, 18.25], "0.1667": [2.81, 17.39, 18.25], "0.25": [-28.12, 17.39, 18.25], "0.3333": [-42.19, 17.39, 18.25], "0.4167": [-30.83, 17.39, 18.25], "0.5": [-4.66, 17.39, 18.25], "0.5833": [24.48, 17.39, 18.25], "0.6667": [44.73, 17.39, 18.25], "0.7083": [47.81, 17.39, 18.25]}}, "left_leg": {"rotation": {"0.0": [22.5, 0, 0], "0.0833": [26.28, 0, 0], "0.1667": [25.74, 0, 0], "0.2083": [22.5, 0, 0], "0.25": [-1.41, 0, 0], "0.2917": [-22.5, 0, 0], "0.3333": [-22.83, 0, 0], "0.4167": [-17.58, 0, 0], "0.5": [-8.4, 0, 0], "0.5833": [0, 0, 0], "0.6667": [16.67, 0, 0], "0.7083": [22.5, 0, 0]}, "position": {"0.0": [1, 0, 2], "0.0833": [1, -0.06, 1.48], "0.125": [1, 0, 1], "0.1667": [1, 0.5, -0.06], "0.2083": [1, 1, -1], "0.25": [1, 1.19, -1.12], "0.2917": [1, 1, -1], "0.3333": [1, 0.58, -1.05], "0.4167": [1, -0.65, -1.14], "0.4583": [1, -1, -1], "0.5": [1, -0.94, -0.44], "0.5833": [1, -0.25, 1], "0.6667": [1, -0.05, 1.85], "0.7083": [1, 0, 2]}}, "right_leg": {"rotation": {"0.0": [-22.5, 0, 0], "0.0833": [-20, 0, 0], "0.1667": [-10, 0, 0], "0.25": [0, 0, 0], "0.3333": [12.66, 0, 0], "0.4167": [22.5, 0, 0], "0.5": [26.28, 0, 0], "0.5833": [25.74, 0, 0], "0.625": [22.5, 0, 0], "0.6667": [-1.41, 0, 0], "0.7083": [-22.5, 0, 0]}, "position": {"0.0": [-1, 1, -1], "0.0833": [-1, -1, -1], "0.1667": [-1, -0.77, -0.06], "0.25": [-1, -0.25, 1], "0.3333": [-1, -0.08, 1.69], "0.4167": [-1, 0, 2], "0.5": [-1, 0, 1], "0.5833": [-1, 0.7, -0.44], "0.625": [-1, 1, -1], "0.6667": [-1, 1.19, -1.13], "0.7083": [-1, 1, -1]}}, "legs": {"position": [0, 0, 0]}, "eyes": {"rotation": [0, 0, 0], "position": [0, 0, 0], "scale": 1}, "sword": {"rotation": [45, 0, 0], "position": [0, -3.5, 0]}}}, "animation.better_piglin_brute.run": {"loop": true, "animation_length": 0.5, "bones": {"head": {"rotation": {"0.0": [12.52, 0.26, 2.17], "0.0833": [-0.09, 0.26, 1.35], "0.1667": [17.41, 0.26, 1.35], "0.25": [12.52, 0.26, 2.17], "0.3333": [-0.09, 0.26, 1.35], "0.4167": [17.41, 0.26, 1.35], "0.5": [12.52, 0.26, 2.17]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0]}}, "left_ear": {"rotation": {"0.0": [-15.28, 0, -15], "0.0833": [25.86, 0, -15], "0.1667": [-10.58, 0, -15], "0.25": [-15.28, 0, -15], "0.3333": [25.86, 0, -15], "0.4167": [-10.58, 0, -15], "0.5": [-15.28, 0, -15]}}, "right_ear": {"rotation": {"0.0": [-15.28, 0, 15], "0.0833": [25.86, 0, 15], "0.1667": [-10.58, 0, 15], "0.25": [-15.28, 0, 15], "0.3333": [25.86, 0, 15], "0.4167": [-10.58, 0, 15], "0.5": [-15.28, 0, 15]}}, "torso": {"rotation": {"0.0": [-9.98, 0, -0.44], "0.0833": [2.52, 0, -0.44], "0.1667": [7.52, 0, -0.44], "0.25": [-9.98, 0, -0.44], "0.3333": [2.52, 0, -0.44], "0.4167": [7.52, 0, -0.44], "0.5": [-9.98, 0, -0.44]}, "position": {"0.0": [0, 1, 0], "0.0833": [0, 1, 0], "0.1667": [0, 1, 0], "0.25": [0, 1, 0], "0.3333": [0, 1, 0], "0.4167": [0, 1, 0], "0.5": [0, 1, 0]}}, "left_arm": {"rotation": {"0.0": [47.52, 8.72, -28.43], "0.0833": [60.51, 8.72, -30.72], "0.1667": [60.51, 8.72, -41.18], "0.25": [47.52, 8.72, -49.35], "0.3333": [34.53, 8.72, -47.07], "0.4167": [34.53, 8.72, -36.61], "0.5": [47.52, 8.72, -28.43]}}, "right_arm": {"rotation": {"0.0": [-129.98, 39.74, -9.78], "0.0833": [-129.98, 39.74, -19.31], "0.1667": [-129.98, 39.74, -19.31], "0.25": [-129.98, 39.74, -9.78], "0.3333": [-129.98, 39.74, -0.26], "0.4167": [-129.98, 39.74, -0.26], "0.5": [-129.98, 39.74, -9.78]}}, "left_leg": {"rotation": {"0.0": [45, 0, 0], "0.0833": [52.5, 0, 0], "0.1667": [2.81, 0, 0], "0.25": [-45, 0, 0], "0.3333": [-22.5, 0, 0], "0.4167": [-2.5, 0, 0], "0.5": [45, 0, 0]}, "position": {"0.0": [1, 1, 3], "0.0833": [1, 1.56, -0.06], "0.1667": [1, 2, -3], "0.25": [1, 2, -3], "0.3333": [1, 0, -3], "0.4167": [1, 0, 4], "0.5": [1, 1, 3]}}, "right_leg": {"rotation": {"0.0": [-45, 0, 0], "0.0833": [-22.5, 0, 0], "0.1667": [-2.5, 0, 0], "0.25": [45, 0, 0], "0.3333": [52.5, 0, 0], "0.4167": [2.81, 0, 0], "0.5": [-45, 0, 0]}, "position": {"0.0": [-1, 2, -3], "0.0833": [-1, 0, -3], "0.1667": [-1, 0, 4], "0.25": [-1, 1, 3], "0.3333": [-1, 1.56, -0.06], "0.4167": [-1, 2, -3], "0.5": [-1, 2, -3]}}, "legs": {"position": [0, 0, 0]}, "eyes": {"rotation": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0]}, "scale": {"0.0": [1, 1, 1], "0.0833": [1, 1, 1], "0.1667": [1, 1, 1], "0.25": [1, 1, 1], "0.3333": [1, 1, 1], "0.4167": [1, 1, 1], "0.5": [1, 1, 1]}}, "sword": {"rotation": {"0.0": [45, 0, 0], "0.0833": [45, 0, 0], "0.1667": [45, 0, 0], "0.25": [45, 0, 0], "0.3333": [45, 0, 0], "0.4167": [45, 0, 0], "0.5": [45, 0, 0]}, "position": {"0.0": [0, -3.5, 0], "0.0833": [0, -3.5, 0], "0.1667": [0, -3.5, 0], "0.25": [0, -3.5, 0], "0.3333": [0, -3.5, 0], "0.4167": [0, -3.5, 0], "0.5": [0, -3.5, 0]}}}}, "animation.better_piglin_brute.damaged": {"loop": true, "animation_length": 0.5, "bones": {"head": {"rotation": {"0.0": [2.5, 0, 0], "0.0833": [25, 0, 0], "0.1667": [22.66, 0, 0], "0.25": [17.08, 0, 0], "0.3333": [10.42, 0, 0], "0.4167": [4.84, 0, 0], "0.5": [2.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0]}}, "left_ear": {"rotation": {"0.0": [0, 0, -22.5], "0.0833": [-45, 0, -22.5], "0.1667": [-12.66, 0, -22.5], "0.25": [22.5, 0, -22.5], "0.3333": [22.5, 0, -22.5], "0.4167": [12.5, 0, -22.5], "0.5": [0, 0, -22.5]}}, "right_ear": {"rotation": {"0.0": [0, 0, 22.5], "0.0833": [-45, 0, 22.5], "0.1667": [-12.66, 0, 22.5], "0.25": [22.5, 0, 22.5], "0.3333": [22.5, 0, 22.5], "0.4167": [12.5, 0, 22.5], "0.5": [0, 0, 22.5]}}, "torso": {"rotation": {"0.0": [2.5, 0, 0], "0.0833": [-15, 0, 0], "0.1667": [-13.18, 0, 0], "0.25": [-8.84, 0, 0], "0.3333": [-3.66, 0, 0], "0.4167": [0.68, 0, 0], "0.5": [2.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0]}}, "left_arm": {"rotation": {"0.0": [-5, 0, -25], "0.0833": [-25, 0, -25], "0.1667": [-33.92, 0, -25], "0.25": [-36.42, 0, -25], "0.3333": [-27.22, 0, -25], "0.4167": [-13.25, 0, -25], "0.5": [-5, 0, -25]}}, "right_arm": {"rotation": {"0.0": [-42.19, 17.39, 18.25], "0.0833": [-37.19, 17.39, 18.25], "0.1667": [-44.45, 17.39, 18.25], "0.25": [-51.21, 17.39, 18.25], "0.3333": [-49.76, 17.39, 18.25], "0.4167": [-45.75, 17.39, 18.25], "0.5": [-42.19, 17.39, 18.25]}}, "left_leg": {"rotation": {"0.0": [0, -45, 0], "0.0833": [2.5, -45, 0], "0.1667": [2.24, -45, 0], "0.25": [1.62, -45, 0], "0.3333": [0.88, -45, 0], "0.4167": [0.26, -45, 0], "0.5": [0, -45, 0]}, "position": {"0.0": [1, 0, 1], "0.0833": [1, 0, 1], "0.1667": [1, 0, 1], "0.25": [1, 0, 1], "0.3333": [1, 0, 1], "0.4167": [1, 0, 1], "0.5": [1, 0, 1]}}, "right_leg": {"rotation": {"0.0": [0, 22.5, 0], "0.0833": [2.5, 22.5, 0], "0.1667": [2.24, 22.5, 0], "0.25": [1.62, 22.5, 0], "0.3333": [0.88, 22.5, 0], "0.4167": [0.26, 22.5, 0], "0.5": [0, 22.5, 0]}, "position": {"0.0": [-1, 0, -1.5], "0.0833": [-1, 0, -1.5], "0.1667": [-1, 0, -1.5], "0.25": [-1, 0, -1.5], "0.3333": [-1, 0, -1.5], "0.4167": [-1, 0, -1.5], "0.5": [-1, 0, -1.5]}}, "legs": {"position": [0, 0, 0]}, "eyes": {"rotation": [0, 0, 0], "position": [0, 0, 0], "scale": {"0.0": [1, 1, 1], "0.0833": [1, 0.3, 1], "0.1667": [1, 0.3728, 1], "0.25": [1, 0.5464, 1], "0.3333": [1, 0.7536, 1], "0.4167": [1, 0.9272, 1], "0.5": [1, 1, 1]}}, "sword": {"rotation": [45, 0, 0], "position": [0, -3.5, 0]}}}, "animation.ptd_dbb_piglin_brute.death": {"loop": "hold_on_last_frame", "animation_length": 2, "bones": {"head": {"rotation": {"0.0": [2.5, 0, 0], "0.0833": [2.5, 0, 0], "0.1667": [2.5, 0, 0], "0.25": [2.5, 0, 0], "0.3333": [2.5, 0, 0], "0.4167": [2.5, 0, 0], "0.5": [2.5, 0, 0], "0.5833": [2.5, 0, 0], "0.6667": [2.5, 0, 0], "0.75": [2.5, 0, 0], "0.8333": [2.5, 0, 0], "0.9167": [2.5, 0, 0], "1.0": [2.5, 0, 0], "1.0833": [2.5, 0, 0], "1.1667": [2.5, 0, 0], "1.25": [2.5, 0, 0], "1.3333": [2.5, 0, 0], "1.4167": [2.5, 0, 0], "1.5": [2.5, 0, 0], "1.5833": [2.5, 0, 0], "1.6667": [2.5, 0, 0], "1.75": [2.5, 0, 0], "1.8333": [2.5, 0, 0], "1.9167": [2.5, 0, 0], "2.0": [2.5, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0], "1.0833": [0, 0, 0], "1.1667": [0, 0, 0], "1.25": [0, 0, 0], "1.3333": [0, 0, 0], "1.4167": [0, 0, 0], "1.5": [0, 0, 0], "1.5833": [0, 0, 0], "1.6667": [0, 0, 0], "1.75": [0, 0, 0], "1.8333": [0, 0, 0], "1.9167": [0, 0, 0], "2.0": [0, 0, 0]}}, "left_ear": {"rotation": {"0.0": [0, 0, -18.5], "0.0417": [22.5, 0, -18.53], "0.0833": [16.88, 0, -18.64], "0.125": [0, 0, -18.8], "0.1667": [-21.67, 0, -18.91], "0.25": [-67.5, 0, -19.67], "0.3333": [-75.14, 1.32, -17.44], "0.4167": [-81, 3.78, -12.18], "0.5": [-81.98, 5.1, -9.36], "0.5833": [-74.97, 3.02, -14.79], "0.625": [-67.5, 0, -22.5], "0.6667": [-25.76, -19.97, -68.74], "0.75": [94.16, -68.9, -199.91], "0.8333": [260.46, -80.92, -366.61], "0.9167": [94.16, -68.9, -199.91], "1.0": [93.13, -61.42, -198.78], "1.0833": [93.07, -60.9, -198.71], "1.1667": [93.02, -60.38, -198.65], "1.25": [92.97, -59.88, -198.6], "1.3333": [92.93, -59.38, -198.55], "1.4167": [92.89, -58.91, -198.5], "1.5": [92.86, -58.45, -198.46], "1.5833": [92.82, -58.02, -198.42], "1.6667": [92.8, -57.63, -198.39], "1.75": [92.77, -57.26, -198.36], "1.8333": [92.75, -56.94, -198.33], "1.9167": [92.73, -56.66, -198.31], "2.0": [92.71, -56.42, -198.28]}}, "right_ear": {"rotation": {"0.0": [0, 0, 26.5], "0.0417": [22.5, 0, 26.47], "0.0833": [16.88, 0, 26.36], "0.125": [0, 0, 26.2], "0.1667": [-21.67, 0, 26.09], "0.25": [-67.5, 0, 25.33], "0.3333": [-75.14, -1.32, 20.74], "0.4167": [-81, -3.78, 13.35], "0.5": [-81.98, -5.1, 9.36], "0.5833": [-74.97, -3.02, 14.64], "0.625": [-67.5, 0, 22.5], "0.6667": [-25.76, 19.97, 69.04], "0.75": [94.16, 68.9, 199.91], "0.8333": [260.46, 80.92, 366.61], "0.9167": [94.16, 68.9, 199.91], "1.0": [93.13, 61.42, 198.78], "1.0833": [93.07, 60.9, 198.71], "1.1667": [93.02, 60.38, 198.65], "1.25": [92.97, 59.88, 198.6], "1.3333": [92.93, 59.38, 198.55], "1.4167": [92.89, 58.91, 198.5], "1.5": [92.86, 58.45, 198.46], "1.5833": [92.82, 58.02, 198.42], "1.6667": [92.8, 57.63, 198.39], "1.75": [92.77, 57.26, 198.36], "1.8333": [92.75, 56.94, 198.33], "1.9167": [92.73, 56.66, 198.31], "2.0": [92.71, 56.42, 198.28]}}, "torso": {"rotation": {"0.0": [2.5, 0, 0], "0.0833": [19.07, 0, 0], "0.125": [25, 0, 0], "0.1667": [26.2, 0, 0], "0.25": [15, 0, 0], "0.3333": [-29.54, 0, 0], "0.375": [-52.5, 0, 0], "0.4167": [-66.68, 0, 0], "0.5": [-90.82, 0, 0], "0.5417": [-97.5, 0, 0], "0.5833": [-98.01, 0, 0], "0.6667": [-88.24, 0, 0], "0.7083": [-85, 0, 0], "0.75": [-86.41, 0, 0], "0.7917": [-90, 0, 0], "0.8333": [-93.12, 0, 0], "0.875": [-95, 0, 0], "0.9167": [-92.81, 0, 0], "0.9583": [-90, 0, 0], "1.0": [-89.91, 0, 0], "1.0833": [-89.77, 0, 0], "1.1667": [-89.68, 0, 0], "1.25": [-89.64, 0, 0], "1.3333": [-89.63, 0, 0], "1.4167": [-89.66, 0, 0], "1.5": [-89.7, 0, 0], "1.5833": [-89.76, 0, 0], "1.6667": [-89.83, 0, 0], "1.75": [-89.89, 0, 0], "1.8333": [-89.95, 0, 0], "1.9167": [-89.99, 0, 0], "2.0": [-90, 0, 0]}, "position": {"0.0": [0, 0.15, 0], "0.0833": [0, 1.27, 1.19], "0.1667": [0, 2.37, 2.5], "0.2083": [0, 2.41, 3.5], "0.25": [0, 2.51, 3.61], "0.3333": [0, 2.92, 3.79], "0.4167": [0, 2.99, 3.95], "0.5": [0, 1.95, 4.09], "0.5833": [0, -6.81, 4.4], "0.625": [0, -10.62, 4.5], "0.6667": [0, -12.06, 4.53], "0.7083": [0, -11.72, 4.5], "0.75": [0, -10.95, 4.5], "0.7917": [0, -10, 4.5], "0.8333": [0, -9.97, 4.5], "0.9167": [0, -9.92, 4.5], "1.0": [0, -9.87, 4.5], "1.0833": [0, -9.84, 4.5], "1.1667": [0, -9.82, 4.5], "1.25": [0, -9.82, 4.5], "1.3333": [0, -9.83, 4.5], "1.4167": [0, -9.85, 4.5], "1.5": [0, -9.88, 4.5], "1.5833": [0, -9.91, 4.5], "1.6667": [0, -9.94, 4.5], "1.75": [0, -9.96, 4.5], "1.8333": [0, -9.98, 4.5], "1.9167": [0, -10, 4.5], "2.0": [0, -10, 4.5]}}, "left_arm": {"rotation": {"0.0": [-5, 0, -23], "0.0833": [-50, 0, -23.07], "0.1667": [-84.93, -7.64, -16.72], "0.2083": [-94.9, -10.86, -14.11], "0.25": [-94.79, -11.48, -13.75], "0.3333": [-86.46, -11.53, -14.12], "0.4167": [-75.63, -10.98, -15.07], "0.4583": [-72.4, -10.86, -15.44], "0.5": [-68.18, -10.86, -15.7], "0.5417": [-72.4, -10.86, -15.96], "0.5833": [-93.92, -8.5, -18.06], "0.625": [-117.4, -10.86, -16.46], "0.6667": [-123.7, -28.95, -7], "0.7083": [-133.08, -48.57, 12.54], "0.75": [-170.59, -56.84, 56.42], "0.7917": [-202.62, -60.68, 92.68], "0.8333": [-190.71, -61.22, 79.42], "0.875": [-174.47, -62.99, 60.68], "0.9167": [-185.55, -73.96, 72.25], "0.9583": [-209.43, -84.95, 97.18], "1.0": [-211.74, -85.41, 99.51], "1.0833": [-216.99, -86.17, 104.81], "1.1667": [-222.95, -86.74, 110.8], "1.25": [-229.46, -87.14, 117.31], "1.3333": [-236.34, -87.39, 124.17], "1.4167": [-243.41, -87.53, 131.21], "1.5": [-250.5, -87.58, 138.28], "1.5833": [-257.43, -87.57, 145.2], "1.6667": [-264.03, -87.51, 151.8], "1.75": [-270.13, -87.45, 157.92], "1.8333": [-275.55, -87.4, 163.37], "1.9167": [-280.12, -87.39, 167.96], "2.0": [-283.66, -87.45, 171.51]}}, "right_arm": {"rotation": {"0.0": [-42.19, 17.39, 16.25], "0.0833": [-44.01, 10.55, 8.8], "0.1667": [-81.55, 10.51, 13.43], "0.2083": [-94.9, 10.86, 15.7], "0.25": [-95.14, 10.87, 16.07], "0.3333": [-86.83, 10.87, 16.07], "0.4167": [-75.7, 10.86, 15.76], "0.4583": [-72.4, 10.86, 15.7], "0.5": [-68.18, 10.86, 15.7], "0.5417": [-72.4, 10.86, 15.7], "0.5833": [-93.92, 8.5, 17.54], "0.625": [-117.4, 10.86, 15.7], "0.6667": [-118.66, 27.82, 11.23], "0.7083": [-133.08, 48.57, -13.76], "0.75": [-225.37, 65.75, -114.29], "0.7917": [-283.31, 78.84, -177.86], "0.8333": [-149.32, 83.15, -40.91], "0.875": [-24.16, 83.86, 88.51], "0.9167": [-119.78, 85.03, -8.39], "0.9583": [-245.07, 85.39, -135.85], "1.0": [-248.66, 85.38, -139.49], "1.0833": [-253.6, 85.34, -144.53], "1.1667": [-255.9, 85.26, -146.88], "1.25": [-255.97, 85.15, -146.98], "1.3333": [-254.23, 85.01, -145.23], "1.4167": [-251.06, 84.86, -142.03], "1.5": [-246.88, 84.7, -137.82], "1.5833": [-242.1, 84.53, -132.98], "1.6667": [-237.11, 84.37, -127.94], "1.75": [-232.34, 84.22, -123.12], "1.8333": [-228.17, 84.09, -118.91], "1.9167": [-225.03, 83.98, -115.73], "2.0": [-223.31, 83.9, -114]}}, "left_leg": {"rotation": {"0.0": [0, -45, 0], "0.0833": [0.59, -45.53, 0], "0.1667": [1.56, -46.41, 0], "0.25": [1.76, -46.58, 0], "0.3333": [0, -45, 0], "0.4167": [-25, -22.5, 0], "0.5": [-70, -22.5, 0], "0.5833": [-94.04, 0.36, 2.78], "0.6667": [-111.54, 0.36, 2.78], "0.75": [-116.54, 0.36, 2.78], "0.8333": [-89.04, 0.36, 2.78], "0.9167": [-99.04, 0.36, 2.78], "1.0": [-89.04, 0.36, 2.78]}, "position": {"0.0": [1, 0, 1], "0.0833": [1, 0.38, 1], "0.1667": [1, 1, 1], "0.25": [1, 2, 1], "0.3333": [1, 3, 1], "0.4167": [1, 4, 1], "0.5": [1, 5.06, 2], "0.5833": [1, -1, 4], "0.6667": [1, -7, 4], "0.75": [1, -8, 3], "0.8333": [1, -8.03, 2.93], "0.9167": [1, -8.05, 2.86], "1.0": [1, -8.06, 2.78], "1.0833": [1, -8.07, 2.71], "1.1667": [1, -8.07, 2.63], "1.25": [1, -8.07, 2.55], "1.3333": [1, -8.07, 2.48], "1.4167": [1, -8.06, 2.4], "1.5": [1, -8.05, 2.33], "1.5833": [1, -8.04, 2.26], "1.6667": [1, -8.03, 2.19], "1.75": [1, -8.02, 2.14], "1.8333": [1, -8.01, 2.08], "1.9167": [1, -8, 2.04], "2.0": [1, -8, 2]}, "scale": {"0.0": [1, 1, 1], "0.0833": [1, 1.1125, 1], "0.1667": [1, 1.2, 1], "0.25": [1, 1.1125, 1], "0.3333": [1, 1, 1]}}, "right_leg": {"rotation": {"0.0": [0, 22.5, 0], "0.0833": [-3.05, 18.95, 0], "0.1667": [-7.5, 13.75, 0], "0.25": [-11.95, 8.55, 0], "0.3333": [-15, 5, 0], "0.4167": [-15, 5, 0], "0.5": [-60, 5, 0], "0.5833": [-105, 5, 0], "0.6667": [-120, 5, 0], "0.75": [-75, 5, 0], "0.8333": [-105, 5, 0], "0.9167": [-85, 5, 0], "1.0": [-84.58, 5, 0], "1.0833": [-84.44, 5, 0], "1.1667": [-84.55, 5, 0], "1.25": [-84.86, 5, 0], "1.3333": [-85.33, 5, 0], "1.4167": [-85.92, 5, 0], "1.5": [-86.59, 5, 0], "1.5833": [-87.3, 5, 0], "1.6667": [-88.01, 5, 0], "1.75": [-88.68, 5, 0], "1.8333": [-89.26, 5, 0], "1.9167": [-89.71, 5, 0], "2.0": [-90, 5, 0]}, "position": {"0.0": [-1, 0, -1.5], "0.0833": [-1, 0.81, -1.62], "0.1667": [-1, 2, -1.5], "0.25": [-1, 3.5, -0.56], "0.3333": [-1, 5, 0.5], "0.4167": [-1, 7, 1.5], "0.5": [-1, 7, 3.5], "0.5833": [-1, -1, 4.5], "0.6667": [-1, -6, 4.5], "0.75": [-1, -7, 2.5], "0.8333": [-1, -7.5, 2.5], "0.9167": [-1, -7.54, 2.48], "1.0": [-1, -7.58, 2.45], "1.0833": [-1, -7.62, 2.42], "1.1667": [-1, -7.66, 2.38], "1.25": [-1, -7.71, 2.34], "1.3333": [-1, -7.75, 2.29], "1.4167": [-1, -7.79, 2.25], "1.5": [-1, -7.84, 2.21], "1.5833": [-1, -7.88, 2.16], "1.6667": [-1, -7.91, 2.12], "1.75": [-1, -7.95, 2.08], "1.8333": [-1, -7.98, 2.05], "1.9167": [-1, -8, 2.02], "2.0": [-1, -8.02, 2]}}, "legs": {"position": [0, 0, 0]}, "eyes": {"rotation": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0], "1.0833": [0, 0, 0], "1.1667": [0, 0, 0], "1.25": [0, 0, 0], "1.3333": [0, 0, 0], "1.4167": [0, 0, 0], "1.5": [0, 0, 0], "1.5833": [0, 0, 0], "1.6667": [0, 0, 0], "1.75": [0, 0, 0], "1.8333": [0, 0, 0], "1.9167": [0, 0, 0], "2.0": [0, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0833": [0, 0, 0], "0.1667": [0, 0, 0], "0.25": [0, 0, 0], "0.3333": [0, 0, 0], "0.4167": [0, 0, 0], "0.5": [0, 0, 0], "0.5833": [0, 0, 0], "0.6667": [0, 0, 0], "0.75": [0, 0, 0], "0.8333": [0, 0, 0], "0.9167": [0, 0, 0], "1.0": [0, 0, 0], "1.0833": [0, 0, 0], "1.1667": [0, 0, 0], "1.25": [0, 0, 0], "1.3333": [0, 0, 0], "1.4167": [0, 0, 0], "1.5": [0, 0, 0], "1.5833": [0, 0, 0], "1.6667": [0, 0, 0], "1.75": [0, 0, 0], "1.8333": [0, 0, 0], "1.9167": [0, 0, 0], "2.0": [0, 0, 0]}, "scale": {"0.0": [1, 1, 1], "0.0833": [1, 0.3, 1], "0.1667": [1, 1.4, 1], "0.2083": [1, 1.9, 1], "0.25": [1, 1.9962, 1], "0.3333": [1, 2.0854, 1], "0.4167": [1, 2.0487, 1], "0.5": [1, 1.9, 1], "0.5833": [1, 0.8778, 1], "0.625": [1, 0.5, 1], "0.6667": [1, 0.5778, 1], "0.75": [1, 1, 1], "0.8333": [1, 0.7704, 1], "0.9167": [1, 0.3852, 1], "1.0": [1, 0.1, 1], "1.0833": [1, 0.0685, 1], "1.1667": [1, 0.0479, 1], "1.25": [1, 0.0367, 1], "1.3333": [1, 0.0333, 1], "1.4167": [1, 0.0362, 1], "1.5": [1, 0.0437, 1], "1.5833": [1, 0.0544, 1], "1.6667": [1, 0.0667, 1], "1.75": [1, 0.0789, 1], "1.8333": [1, 0.0896, 1], "1.9167": [1, 0.0971, 1], "2.0": [1, 0.1, 1]}}, "sword": {"rotation": [45, 0, 0], "position": [0, -3.5, 0]}}}, "animation.ptd_dbb_piglin_brute.dead": {"loop": true, "bones": {"head": {"rotation": {"0.0": {"post": [2.5, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}}, "left_ear": {"rotation": {"0.0": {"post": [92.70905, -56.42285, -198.2849], "lerp_mode": "catmullrom"}}}, "right_ear": {"rotation": {"0.0": {"post": [92.70905, 56.42285, 198.2849], "lerp_mode": "catmullrom"}}}, "torso": {"rotation": {"0.0": {"post": [-90, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, -10, 4.5], "lerp_mode": "catmullrom"}}}, "left_arm": {"rotation": {"0.0": {"post": [-283.65881, -87.45052, 171.50853], "lerp_mode": "catmullrom"}}}, "right_arm": {"rotation": {"0.0": {"post": [-223.3124, 83.89871, -113.99777], "lerp_mode": "catmullrom"}}}, "left_leg": {"rotation": {"0.0": {"post": [-89.03969, 0.36092, 2.77929], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [1, -8, 2], "lerp_mode": "catmullrom"}}, "scale": {"0.0": {"post": [1, 1, 1], "lerp_mode": "catmullrom"}}}, "right_leg": {"rotation": {"0.0": {"post": [-90, 5, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [-1, -8.025, 2], "lerp_mode": "catmullrom"}}}, "legs": {"position": {"0.0": {"post": [0, "math.sin((q.anim_time)*180)*0.1", 0], "lerp_mode": "catmullrom"}}}, "eyes": {"rotation": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "position": {"0.0": {"post": [0, 0, 0], "lerp_mode": "catmullrom"}}, "scale": {"0.0": {"post": [1, 0.1, 1], "lerp_mode": "catmullrom"}}}, "sword": {"rotation": [45, 0, 0], "position": [0, -3.5, 0]}}}}}