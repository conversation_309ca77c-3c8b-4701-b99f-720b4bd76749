/**
 * The total animation time in ticks / length of the slowness effect
 */
const ANIMATION_TIME = 33; // Total attack animation time in ticks (0.6667 * 50 = 33.33)
/**
 * Handles all mechanics for the Piglin Brute
 * This includes attack behavior similar to winged zombie but for a walking entity
 *
 * @param piglinBrute The piglin brute entity
 */
export function piglinBruteMechanics(piglinBrute) {
    // Skip if entity is not valid
    try {
        if (!piglinBrute)
            return;
        // Skip if entity is spawning or dead
        const isSpawning = piglinBrute.getProperty("ptd_dbb:spawning");
        const isDead = piglinBrute.getProperty("ptd_dbb:dead");
        if (isSpawning || isDead)
            return;
        // Apply slowness effect to prevent movement during the attack
        piglinBrute.addEffect("minecraft:slowness", ANIMATION_TIME, { amplifier: 250, showParticles: false });
        // The delayed_attack behavior handles the actual attack mechanics
        // We just need to ensure the entity doesn't move during attacks
    }
    catch (e) {
        return;
    }
    return;
}
