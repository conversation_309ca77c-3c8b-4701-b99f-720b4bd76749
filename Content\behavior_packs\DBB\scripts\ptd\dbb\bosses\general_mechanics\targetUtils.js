import { GameMode } from "@minecraft/server";
import { getDistance } from "../../utilities/vector3";
/**
 * Acquires valid targets for boss entities
 * Uses a strict prioritization system where higher priority targets are always selected regardless of distance
 * Priority order: Players (0) > Boss<PERSON> (1) > Minions (3)
 *
 * As long as a higher priority target exists, it will always be returned regardless of distance.
 * Only when there are no valid targets in a priority level will the function check the next priority level.
 *
 * @param entity The boss entity instance
 * @param location Current position of the boss
 * @param searchDistance Maximum distance to search for targets (default: 32)
 * @param excludeFamily Family to exclude from target search (e.g., "necromancer", "piglin_champion")
 * @returns The selected target entity or undefined if no valid target
 */
export function getTarget(entity, location, searchDistance = 32, excludeFamilies = []) {
    // Don't look for targets if spawning
    if (entity.getProperty("ptd_dbb:spawning"))
        return undefined;
    // Priority 0: Get the nearest player within searchDistance blocks, excluding creative and spectator modes
    const targetPlayers = entity.dimension.getPlayers({
        location,
        maxDistance: searchDistance,
        excludeGameModes: [GameMode.creative, GameMode.spectator]
    });
    // Priority 1: Get boss entities (except the specified family)
    const targetBosses = entity.dimension.getEntities({
        location,
        maxDistance: searchDistance,
        families: ["boss"],
        excludeFamilies: excludeFamilies ? excludeFamilies : []
    });
    // Priority 3: Get minion entities
    const targetMinions = entity.dimension.getEntities({
        location,
        maxDistance: searchDistance,
        families: ["minion"],
        excludeFamilies: excludeFamilies ? excludeFamilies : []
    });
    // Find the closest player
    let closestPlayer = undefined;
    let closestPlayerDistance = searchDistance;
    for (const player of targetPlayers) {
        const distance = getDistance(location, player.location);
        if (distance < closestPlayerDistance) {
            closestPlayer = player;
            closestPlayerDistance = distance;
        }
    }
    // Find the closest boss that is not spawning and not dead
    let closestBoss = undefined;
    let closestBossDistance = searchDistance;
    for (const boss of targetBosses) {
        // Check if the boss is spawning or dead
        const isSpawning = boss.getProperty("ptd_dbb:spawning");
        const isDead = boss.getProperty("ptd_dbb:dead");
        // Only consider bosses that are not spawning and not dead
        if (!isSpawning && !isDead) {
            const distance = getDistance(location, boss.location);
            if (distance < closestBossDistance) {
                closestBoss = boss;
                closestBossDistance = distance;
            }
        }
    }
    // Find the closest minion
    let closestMinion = undefined;
    let closestMinionDistance = searchDistance;
    for (const minion of targetMinions) {
        const distance = getDistance(location, minion.location);
        if (distance < closestMinionDistance) {
            closestMinion = minion;
            closestMinionDistance = distance;
        }
    }
    // Strict prioritization: higher priority targets are always selected regardless of distance
    if (closestPlayer) {
        return closestPlayer;
    }
    else if (closestBoss) {
        return closestBoss;
    }
    else if (closestMinion) {
        return closestMinion;
    }
    else {
        return undefined;
    }
}
