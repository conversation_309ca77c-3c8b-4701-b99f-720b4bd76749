import { spawnEntitiesWithInterval } from "../../../utilities/summonEntity";
import { getRandomLocation } from "../../../utilities/vector3";
/**
 * Executes the summoning chant attack for the Piglin Champion
 * Summons 3 piglin minions progressively
 *
 * @param piglinChampion The piglin champion entity
 */
export async function executeSummoningChantAttack(piglinChampion) {
    // Configure the entities to spawn
    const entityConfigs = [
        {
            entityId: "ptd_dbb:piglin_brute",
            count: 3
        }
    ];
    // Spawn the minions with a delay between each
    await spawnEntitiesWithInterval(piglinChampion.dimension, entityConfigs, () => {
        // Use getRandomLocation to get a random position around the piglin
        // baseOffset: 3, additionalOffset: 2, randomYOffset: 0, checkForAirBlock: true
        const pos = getRandomLocation(piglinChampion.location, piglinChampion.dimension, 3, // Base offset (minimum distance from piglin)
        4, // Additional offset (random extra distance)
        0, // No Y offset
        true // Check for air block
        );
        // If we got a valid position, add visual effects
        if (pos) {
            piglinChampion.dimension.spawnParticle("minecraft:large_explosion", pos);
        }
        return pos;
    }, 15, // 15 ticks delay between spawns (0.75 seconds)
    (entity) => {
        // Optional callback when an entity is spawned
        // Play a sound and visual effect
        piglinChampion.dimension.playSound("random.totem", entity.location, { volume: 200, pitch: 0.9 });
        piglinChampion.dimension.spawnParticle("ptd_dbb:pg_summon2_01", entity.location);
    });
    return;
}
